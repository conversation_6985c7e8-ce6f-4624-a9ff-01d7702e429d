<VirtualHost *:80>
    ServerName hemanicanada.com
    ServerAlias hemanicanada.com
    DocumentRoot /var/www/html/sites/ordrz.com/public
    Protocols h2 http/1.1
    <Directory /var/www/html/sites/ordrz.com/public>
        Options -Indexes +FollowSymLinks +MultiViews
        AllowOverride All
        Require all granted
    </Directory>

#    <FilesMatch \.php$>
#        SetHandler "proxy:unix:/run/php/php8.0-fpm.sock|fcgi://localhost/"
#    </FilesMatch>

    ErrorLog ${APACHE_LOG_DIR}/ordrz.com-error.log
    CustomLog ${APACHE_LOG_DIR}/ordrz.com-access.log combined
</VirtualHost>

