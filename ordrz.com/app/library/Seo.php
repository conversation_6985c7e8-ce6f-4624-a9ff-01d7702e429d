<?php

namespace Seo;

class Seo
{
    public function getSeo($id, $page)
    {
        $data = [];
        try {
            // Generate the URL for the XML file
            $url = S3_BUCKET . 'seo/' . $id . '.xml';
            // Validate URL
            if (!filter_var($url, FILTER_VALIDATE_URL)) {
                error_log('Invalid SEO XML URL format: ' . $url);
                return $data;
            }

            // Load the XML file with proper error handling
            libxml_use_internal_errors(true);
            $file = simplexml_load_file($url);

            if ($file === false) {
                $errors = libxml_get_errors();
                libxml_clear_errors();
                error_log('Failed to load the SEO XML file: ' . $url . '. Errors: ' . print_r($errors, true));
                return $data;
            }
            // Iterate over the pages in the XML
        //   echo "<pre>";
        //     print_r($file->page);
        //     die();
                $found = false;
            foreach ($file->page as $key) {
                if ($key['name'] === $page) {
                    $data['gtitle'] = (string)$key->generated->pagetitle;
                    $data['gh1'] = (string)$key->generated->h1;
                    $data['gky'] = (string)$key->generated->keywords;
                    $data['gdesc'] = (string)$key->generated->desc;
                    $found = true;
                    break;
                }
            }
            // If the page wasn't found, log the error
            if (!$found) {
                error_log('Page not found in SEO XML: ' . $page . ' for ID: ' . $id);
            }
  
            return ;

        } catch (\Exception $e) {
            error_log('SEO XML Processing Error: ' . $e->getMessage());
            return $data;
        }
    }

        function uploadToS3($tmp_name, $file_type, $file_name, $img_type = "site")
        {
            $url = S3_IMAGE_UPLOAD_URL;
            $postField = array();
            $postField['files'] =  curl_file_create($tmp_name, $file_type, $file_name);
            $postField['im_type'] = 'web';
            $postField['image_type'] = $img_type;
            $postField['resize'] = '1';

            $headers = array("Content-Type" => "multipart/form-data");
            $curl_handle = curl_init();
            curl_setopt($curl_handle, CURLOPT_URL, $url);

            curl_setopt($curl_handle, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($curl_handle, CURLOPT_POST, TRUE);
            curl_setopt($curl_handle, CURLOPT_POSTFIELDS, $postField);
            curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, TRUE);
            $returned_fileName = curl_exec($curl_handle);
            curl_close($curl_handle);

            $result = json_decode($returned_fileName);
            return $result[0]->imageUrl;
        }
}