<?php

namespace Obw;

class Obw
{
    /**
     * Get the business details based on the provided domain name.
     * 
     * @param array $name Contains 'businessUsername' and 'flag' for the request.
     * @return array Returns the details of the restaurant (business) fetched from an external API.
     */
    public function getBusinessDetail($name) {
        // Extract the flag (0 or 1) from the $name array
        $flag = $name['flag'];
        $restaurantDetail = $this->businessDetails("adanakebab",0);
        return $restaurantDetail['result'];
    }

    /**
     * Fetch business details from the external API using the business username and flag.
     * 
     * @param string $username The username of the business (restaurant) to look up.
     * @param int $flag A flag to determine the type of business (could be used for differentiating environments).
     * @return array The decoded JSON response from the external API containing business details.
     */
    public function businessDetails($username, $flag) {
        // Build the API URL by concatenating the username and flag into the API's endpoint
        $url = "https://td0c8x9qb3.execute-api.us-east-1.amazonaws.com/prod/v1/business/theme/" . $username . "/flag/" . $flag;
        
        // Send a GET request to the API and store the JSON response as a string
        $restaurant = file_get_contents($url);
        
        // Decode the JSON string into an associative array
        $restaurantDetail = json_decode($restaurant, true);
        
        // Return the decoded restaurant details
        return $restaurantDetail;
    }

    /**
     * Determine the business username and flag based on the given domain name.
     * 
     * @param string $name The full domain name (e.g. "example.ordrz.com").
     * @return array Returns an array containing the 'businessUsername' and 'flag'.
     */
    public function domainDetails($name) {
        // Initialize flag to 0 (default) and an empty string for the business username
        $flag = 0;
        $businessUsername = "";
        
        // Check if the domain matches one of the following patterns:
        // - [businessUsername].staging.ordrz.com
        // - [businessUsername].ordrz.com
        // - [businessUsername].ordrz.localhost
        if (preg_match("/^([^.]+)\.staging\.ordrz\.com/", $name) || 
            preg_match("/^([^.]+)\.ordrz\.com/", $name) || 
            preg_match("/^([^.]+)\.ordrz\.localhost/", $name)) {
            
            // If there's a match, parse the domain to extract the business username
            $url = parse_url($name);
            $path = explode('.', $url['path']);
            
            // The business username will be the first part of the domain (e.g., 'example' from 'example.ordrz.com')
            $businessUsername = trim($path[0]);
            
            // Set flag to 0 (since these are standard domains, not custom ones)
            $flag = 0;
        } else {
            // If the domain does not match the previous patterns, assume it's a custom domain
            
            // Parse the domain to extract the host (domain) part
            $url = parse_url($name);
            $host = $url['path'];
            
            // Remove 'www.' if it's present at the beginning of the domain
            if (strpos($host, 'www.') === 0) {
                $host = substr($host, 4);
            }
            
            // Set the business username as the full domain name
            $businessUsername = $host;
            
            // Set flag to 1 (since this indicates a custom domain)
            $flag = 1;
        }
        
        // Return the business username and flag as an array
        return [
            'businessUsername' => $businessUsername,
            'flag' => $flag
        ];
    }
}
