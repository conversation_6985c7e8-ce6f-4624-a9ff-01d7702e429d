<?php
$theme_settings =  json_decode($businessDetail['theme_settings']);
$popupData = isset($theme_settings->popup) && !empty($theme_settings->popup) ? $theme_settings->popup : "";

if (!empty($popupData)) {
    foreach ($popupData as $key => $value) {

        if ($value->source != "App") {
?>
            <div class="modal fade" id="website_custom_popup" tabindex="-1" role="dialog" aria-labelledby="business_not_open_now" aria-hidden="true">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                    <div class="modal-header" style="padding: 0px !important;">
                        <a class="cursor-pointer ms-auto" data-bs-dismiss="modal" aria-label="Close"><i class="fas fa-times"></i>
                        </a>
                    </div>
                        <div class="modal-body">
                            <a href='<?= $value->url ?>'>
                              <img width="100%" src="<?= $value->popup_url ?>" alt="popup image">
                            </a>
                        </div>
                    </div>
                </div>
            </div>

    <?php
        }
    }
    ?>
    <script>
        $(document).ready(function() {
            if (!sessionStorage.getItem('modalShown')) {
                $("#website_custom_popup").modal("toggle");
                sessionStorage.setItem('modalShown', 'true');
            }
        });
    </script>

<?php
}
?>