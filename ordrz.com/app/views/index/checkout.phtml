<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
   
    <title>checkout</title>
    <?php include __DIR__ . '/partials/navbar.phtml'; ?>
    <!-- Include React-generated CSS file -->
    <?php
    $name = $_SERVER['SERVER_NAME'];  
    if(preg_match("/^([^.]+)\.staging\.ordrz\.com/",$name)){ ?>
    <link rel="stylesheet" href="https://checkout-staging.ordrz.com/static/css/main.a666252e.css">
    <?php
    }else{?>
        <link rel="stylesheet" href="https://checkout.ordrz.com/static/css/main.a666252e.css">
   <?php }
    ?>
    
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans&display=swap" rel="stylesheet" />
    <style>
        .custom_container{
            max-width:1200px  !important
        }
    </style>
</head>

<body>
<?php include __DIR__ . '/partials/header.phtml'; ?>
<!-- Output PHP data as a JavaScript variable -->
<script>
    var businessInfo = <?php echo json_encode($checkoutInfo); ?>;
</script>
    <!-- <button id="placeOrderButton">Place Order</button> -->
    <div id="root"></div>
    <!-- from static build -->
    <?php
    $name = $_SERVER['SERVER_NAME'];  
    if(preg_match("/^([^.]+)\.staging\.ordrz\.com/",$name)){ ?>
 <script src="https://checkout-staging.ordrz.com/static/js/main.b75be690.js"></script>
    <script src="https://checkout-staging.ordrz.com/static/js/179.c3afeb96.chunk.js"></script>
   <?php }else{

    
    ?>
    <script src="https://checkout.ordrz.com/static/js/main.b75be690.js"></script>
    <script src="https://checkout.ordrz.com/static/js/179.c3afeb96.chunk.js"></script>
    <?php 
    }
    ?>
</body>
</html>