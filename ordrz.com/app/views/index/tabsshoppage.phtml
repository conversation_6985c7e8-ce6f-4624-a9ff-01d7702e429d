<?php
$headerPath = (isset($businessDetail['web_theme']) && $businessDetail['web_theme'] != "") ? '/../theme/partials/customHeader.phtml' : '/partials/header.phtml';
include __DIR__ . $headerPath;

 include __DIR__ . '/partials/cartSummary.phtml';
$searchResult = (isset($web_theme) && isset($web_theme['card']['search_results'])) ? $web_theme['card']['search_results'] : '';
$cardsToShow = (isset($searchResult) && isset($searchResult['settings']['cards_to_show'])) ? $searchResult['settings']['cards_to_show'] : '';
$banner_setting = (isset($themeSettings) && isset($themeSettings['theme_settings']['categories_name_center_align'])) ? $themeSettings['theme_settings']['categories_name_center_align'] : '';
$product_card_path = (isset($web_theme) &&  !empty($web_theme)) ? '/../theme/partials/customCards.phtml' : '/partials/productCard.phtml';
$logo = isset($businessDetail['logo_image']) ? $businessDetail['logo_image'] : 'no_image.jpg';
?>


<section class="sub_page_banner01"></section>
<div class="cat_tab_slider_main2 position-sticky d-inline-block w-100">
    <div class="tabs_custom_container">
        <div style="grid-template-columns:1fr;border:unset" class="cat_tab_slider_parent position-relative">
            <ul class="nav cat_tab_slider2 d-flex align-items-center justify-content-start flex-nowrap overflow-x-scroll overflow-y-hidden position-relative">
                <?php
                // Get the current URL path segments
                $currentUrl = $_SERVER['REQUEST_URI'];
                $pathSegments = array_filter(explode('/', $currentUrl));
                $lastSegment = end($pathSegments);
              
                $activeTabId = null;
                 // Default to "All" tab being active
                 // We'll set this to false if we find a matching category
                $isAllActive =  count($pathSegments) > 1 ? false : true;
     
                ?>
                <li>
                    <a class="scrolling_tab all_tab <?php echo $isAllActive ? 'active' : ''; ?>"
                    data-id="0">
                        All
                    </a>
                </li>
                <?php
                if (isset($categories) && $categories != "") {
                    foreach ($categories as $category) {
                        if (isset($category['item_count']) && $category['item_count'] != "0") {
                            // Get the category slug
                            $categorySlug = empty($category['slug'])
                                ? strtolower(trim(
                                    preg_replace('/[\s-]+/', '-',
                                        preg_replace('/[^A-Za-z0-9-]+/','-',
                                            preg_replace('/[&]/', '-',
                                                preg_replace('/[\']/', '-',
                                                    iconv('UTF-8', 'ASCII//TRANSLIT', $category['category_name'])
                                                )
                                            )
                                        )
                                    ),
                                '-'))
                                : $category['slug'];

                            // Check if this category matches the last URL segment
                            $isActive = ($lastSegment === $categorySlug);
                            if ($isActive) {
                                $isAllActive = false; // If we found a match, "All" tab is not active
                                $activeTabId = $categorySlug;
                            }
                ?>
                        <li><a class="scrolling_tab <?php echo $isActive ? 'active' : ''; ?>"
                            data-id="<?php echo $categorySlug; ?>"><?= $category['category_name'] ?></a></li>
                <?php
                    }
                    }
                } ?>
            </ul>
            <div class="scrolling_tabs_arrows position-absolute w-100 h-100 d-flex align-items-center justify-content-between">
                <button class="left_arrow" onclick="rightScrollShopPage()"> <i class="fa fa-angle-left" aria-hidden="true"></i></button>
                <button class="right_arrow" onclick="leftScrollShopPage()"><i class="fa fa-angle-right"></i></button>
            </div>
        </div>
    </div>
</div>

<div class="product_listing_section w-100 d-block">
    <div class="tabs_custom_container">
        <div class="shop_product_list_parent d-flex flex-column">

            <?php
            $displayed_categories = [];
                if (isset($items) && count($items) > 1) {
                    $category_name = "";
                    foreach ($items as $item) {
                        if (!in_array($item['category'], $displayed_categories)) {
                            $displayed_categories[] = $item['category'];
                            if ($category_name !== $item['category']) {
                                if ($category_name !== "") {
                                    echo '</div></div>';
                                }
                                $category_name = $item['category'];
                                $category_image = isset($item['menu_cat_image']) ? $item['menu_cat_image'] : '';
                            ?>
                                    <?php
                                    // Get the category slug for this section
                                    $categorySectionSlug = empty($item['category_slug'])
                                        ? strtolower(trim(preg_replace(
                                            '/[\s-]+/', '-',
                                            preg_replace(
                                                '/[^A-Za-z0-9-]+/', '-',
                                                preg_replace(
                                                    '/[&]/', '-',
                                                    preg_replace(
                                                        '/[\']/', '-',
                                                        iconv('UTF-8', 'ASCII//TRANSLIT', $item['category'])
                                                    )
                                                )
                                            )
                                        ), '-'))
                                        : $item['category_slug'];

                                    // Check if this category should be hidden based on the active tab
                                    $hideCategoryClass = '';
                                    if ($activeTabId !== null && $categorySectionSlug !== $activeTabId) {
                                        $hideCategoryClass = 'd-none';
                                    }
                                    ?>
                                    <div class="category_section <?php echo $hideCategoryClass; ?>" id="<?php echo $categorySectionSlug; ?>">
                                    <?php if (isset($category_image) && isset($banner_setting) && $category_image != "" && $banner_setting == "1") {?>
                                    <div class='category-banner-section'>
                                        <img src="<?php echo$category_image?>" alt="<?php echo isset($item['category']) ? $item['category'] : ''; ?>" class="tabs-image" style="width: 100%; height:100%">
                                        <div class="shop_product_list_heading d-flex justify-content-start">
                                        <h3 class="category_name"><?php echo $item['category']; ?></h3>
                                    </div>
                                     </div>
                                                    <?php }else{ ?>
                                    <div class="shop_product_list_heading d-flex justify-content-start">
                                        <h3 class="category_name"><?php echo $item['category']; ?></h3>
                                    </div>
                                    <?php } ?>
                                    <div class="shop_product_listing row">
                                <?php
                            }
                        }
                        $image_name = explode('/', $item['image']);
                        $product_name = end($image_name);
                        $item_name = strtolower($item['name']);
                        $delimiter = "-";
                        $slug = strtolower(trim(preg_replace('/[\s-]+/', $delimiter, preg_replace('/[^A-Za-z0-9-]+/', $delimiter, preg_replace('/[&]/', '', preg_replace('/[\']/', '', iconv('UTF-8', 'ASCII//TRANSLIT', $item_name))))), $delimiter));
                        $slugWithId = $slug . '-' . $item['menu_item_id'];
                        include __DIR__ . $product_card_path;
                                ?>

                    <?php
                    }
                    echo '</div></div>';
                } else {
                    include __DIR__ . '/./partials/404Page.phtml';
                }

                    ?>
                                    </div>
                                </div>
        </div>
        <?php
if (isset($web_theme) &&  !empty($web_theme)) {
  $theme['footer'] = $web_theme['footer'];
  $footerPath ='/../theme/partials/customfooter.phtml';
}else{
  $footerPath ='/partials/footer.phtml';
}
include __DIR__ . $footerPath;
?>
<script>
    $(document).ready(function(){
        $('.scrolling_tab').on('click', function(){
            var tabID = $(this).data('id');
            handleActiveTab(tabID);
            $('.scrolling_tab').removeClass('active');
            $(this).toggleClass('active');
            // Update URL with the tab ID without reloading the page
            if (history.pushState) {
                // Get the current URL
                const currentUrl = window.location.href;
                const urlObj = new URL(currentUrl);
                const pathSegments = urlObj.pathname.split('/').filter(segment => segment);

                // Preserve important path segments like "menu" or "shop"
                let basePathSegments = [];

                // Check if we have at least one path segment (menu or shop)
                if (pathSegments.length > 0) {
                    // Keep the first segment (usually "menu" or "shop")
                    basePathSegments.push(pathSegments[0]);

                    // If there's a second segment that's not a category, keep it too
                    // This handles cases like /menu/location/ etc.
                    if (pathSegments.length > 1 && !$('.scrolling_tab[data-id="' + pathSegments[1] + '"]').length) {
                        basePathSegments.push(pathSegments[1]);
                    }
                }

                // Create the base URL with preserved path segments
                const baseUrl = urlObj.origin + '/' + basePathSegments.join('/');

                // Create the new URL with the tab ID
                let newUrl;
                if (tabID === "0" || tabID === 0) {
                    // If "All" tab is selected, use the base URL without a tab ID
                    newUrl = baseUrl;
                } else {
                    // Otherwise, append the tab ID to the URL
                    newUrl = baseUrl + '/' + tabID;
                }

                // Update the browser history
                window.history.pushState({ tabID: tabID }, '', newUrl);
            }
        });

        // The initial tab selection is now handled by PHP
        // No need to run JavaScript for initial selection since PHP has already set the active classes
        // This prevents any flicker that might occur when JavaScript runs

        // Handle browser back/forward buttons
        window.addEventListener('popstate', function(event) {
            if (event.state && event.state.tabID) {
                // If there's a tab ID in the state, activate that tab
                const tabID = event.state.tabID;
                handleActiveTab(tabID);
                $('.scrolling_tab').removeClass('active');
                $('.scrolling_tab[data-id="' + tabID + '"]').addClass('active');
            } else {
                // If no tab ID in state, check the URL for a tab ID
                const currentUrl = window.location.href;
                const urlObj = new URL(currentUrl);
                const pathSegments = urlObj.pathname.split('/').filter(segment => segment);

                // Check all path segments to find one that matches a tab ID
                let foundTabId = null;

                // Loop through all path segments from last to first
                for (let i = pathSegments.length - 1; i >= 0; i--) {
                    const segment = pathSegments[i];
                    // Check if this segment matches a tab ID
                    const matchingTab = $('.scrolling_tab[data-id="' + segment + '"]');
                    if (matchingTab.length > 0) {
                        foundTabId = segment;
                        break;
                    }
                }

                if (foundTabId) {
                    // If we found a matching tab ID in the URL, activate that tab
                    handleActiveTab(foundTabId);
                    $('.scrolling_tab').removeClass('active');
                    $('.scrolling_tab[data-id="' + foundTabId + '"]').addClass('active');
                } else {
                    // If no tab ID found, activate the "All" tab
                    handleActiveTab("0");
                    $('.scrolling_tab').removeClass('active');
                    $('.scrolling_tab[data-id="0"]').addClass('active');
                }
            }
        });
    })

    function leftScrollShopPage() {
    const left = document.querySelector(".cat_tab_slider2");
    left.scrollBy(200, 0);
    }
    function rightScrollShopPage() {
    const right = document.querySelector(".cat_tab_slider2");
    right.scrollBy(-200, 0);
    }
    function handleActiveTab(tabID){
        // Check if it's the "All" tab (handle both string "0" and number 0)
        const isAllTab = (tabID === "0" || tabID === 0);

        if (!isAllTab) {
            $('.category_section').addClass('d-none');
            $('#' + tabID).removeClass('d-none');
        } else {
            // Show all categories for the "All" tab
            $('.category_section').removeClass('d-none');
        }

        // Scroll to the selected category if it's not the "All" tab
        if (!isAllTab) {
            const targetElement = document.getElementById(tabID);
            if (targetElement) {
                // Smooth scroll to the category with a small offset for better visibility
                $('html, body').animate({
                    scrollTop: $(targetElement).offset().top - 100
                }, 500);
            }
        } else {
            // Scroll back to the top when "All" is selected
            $('html, body').animate({
                scrollTop: $('.cat_tab_slider_main2').offset().top - 50
            }, 500);
        }
    }
</script>