<?php
$headerPath = (isset($businessDetail['web_theme']) && $businessDetail['web_theme'] != "") ? '/../theme/partials/customHeader.phtml' : '/partials/header.phtml';
include __DIR__ . $headerPath;

 include __DIR__ . '/partials/cartSummary.phtml';
$searchResult = (isset($web_theme) && isset($web_theme['card']['search_results'])) ? $web_theme['card']['search_results'] : '';
$cardsToShow = (isset($searchResult) && isset($searchResult['settings']['cards_to_show'])) ? $searchResult['settings']['cards_to_show'] : '';
$banner_setting = (isset($themeSettings) && isset($themeSettings['theme_settings']['categories_name_center_align'])) ? $themeSettings['theme_settings']['categories_name_center_align'] : '';
$product_card_path = (isset($web_theme) &&  !empty($web_theme)) ? '/../theme/partials/customCards.phtml' : '/partials/productCard.phtml';
$logo = isset($businessDetail['logo_image']) ? $businessDetail['logo_image'] : 'no_image.jpg';
?>


<section class="sub_page_banner01"></section>
<div class="cat_tab_slider_main2 position-sticky d-inline-block w-100">
    <div class="tabs_custom_container">
        <div style="grid-template-columns:1fr;border:unset" class="cat_tab_slider_parent position-relative">
            <ul class="nav cat_tab_slider2 d-flex align-items-center justify-content-start flex-nowrap overflow-x-scroll overflow-y-hidden position-relative">
                <li>
                    <a class="scrolling_tab all_tab active" 
                    data-id="0">
                        All
                    </a>
                </li>
                <?php
                if (isset($categories) && $categories != "") {
                    foreach ($categories as $category) {
                        if (isset($category['item_count']) && $category['item_count'] != "0") {
                ?>
                        <li><a class="scrolling_tab" data-id="<?php 
                            if (empty($category['slug'])) {
                                echo strtolower(trim(
                                    preg_replace('/[\s-]+/', '-', 
                                        preg_replace('/[^A-Za-z0-9-]+/','-', 
                                            preg_replace('/[&]/', '-', 
                                                preg_replace('/[\']/', '-', 
                                                    iconv('UTF-8', 'ASCII//TRANSLIT', $category['category_name'])
                                                )
                                            )
                                        )
                                    ), 
                                '-'));
                            } else {
                                echo $category['slug'];
                            }
                        ?>"><?= $category['category_name'] ?></a></li>
                <?php
                    }
                    }
                } ?>
            </ul>
            <div class="scrolling_tabs_arrows position-absolute w-100 h-100 d-flex align-items-center justify-content-between">
                <button class="left_arrow" onclick="rightScrollShopPage()"> <i class="fa fa-angle-left" aria-hidden="true"></i></button>
                <button class="right_arrow" onclick="leftScrollShopPage()"><i class="fa fa-angle-right"></i></button>
            </div>
        </div>
    </div>
</div>

<div class="product_listing_section w-100 d-block">
    <div class="tabs_custom_container">
        <div class="shop_product_list_parent d-flex flex-column">

            <?php
            $displayed_categories = [];    
                if (isset($items)) {
                    $category_name = "";
                    foreach ($items as $item) {
                        if (!in_array($item['category'], $displayed_categories)) {
                            $displayed_categories[] = $item['category'];
                            if ($category_name !== $item['category']) {
                                if ($category_name !== "") {
                                    echo '</div></div>';
                                }
                                $category_name = $item['category'];
                                $category_image = isset($item['menu_cat_image']) ? $item['menu_cat_image'] : '';
                            ?>
                                    <div class="category_section" id="<?php
                                        if (empty($item['category_slug'])) {
                                            echo strtolower(trim(preg_replace(
                                                '/[\s-]+/', '-', 
                                                preg_replace(
                                                    '/[^A-Za-z0-9-]+/', '-', 
                                                    preg_replace(
                                                        '/[&]/', '-', 
                                                        preg_replace(
                                                            '/[\']/', '-', 
                                                            iconv('UTF-8', 'ASCII//TRANSLIT', $item['category'])
                                                        )
                                                    )
                                                )
                                            ), '-'));
                                        } else {
                                            echo $item['category_slug'];
                                        }
                                    ?>">
                                    <?php if (isset($category_image) && isset($banner_setting) && $category_image != "" && $banner_setting == "1") {?>
                                    <div class='category-banner-section'>
                                        <img src="<?php echo$category_image?>" alt="<?php echo isset($item['category']) ? $item['category'] : ''; ?>" class="tabs-image" style="width: 100%; height:100%">
                                        <div class="shop_product_list_heading d-flex justify-content-start">
                                        <h3 class="category_name"><?php echo $item['category']; ?></h3>
                                    </div>
                                     </div>
                                                    <?php }else{ ?>
                                    <div class="shop_product_list_heading d-flex justify-content-start">
                                        <h3 class="category_name"><?php echo $item['category']; ?></h3>
                                    </div>
                                    <?php } ?>
                                    <div class="shop_product_listing row">
                                <?php
                            }
                        }
                        $image_name = explode('/', $item['image']);
                        $product_name = end($image_name);
                        $item_name = strtolower($item['name']);
                        $delimiter = "-";
                        $slug = strtolower(trim(preg_replace('/[\s-]+/', $delimiter, preg_replace('/[^A-Za-z0-9-]+/', $delimiter, preg_replace('/[&]/', '', preg_replace('/[\']/', '', iconv('UTF-8', 'ASCII//TRANSLIT', $item_name))))), $delimiter));
                        $slugWithId = $slug . '-' . $item['menu_item_id'];
                        include __DIR__ . $product_card_path;
                                ?>

                    <?php
                    }
                    echo '</div></div>';
                } else {
                    echo "<p>No items available.</p>";
                }
          
                    ?>
                                    </div>
                                </div>
        </div>
        <?php
if (isset($web_theme) &&  !empty($web_theme)) {
  $theme['footer'] = $web_theme['footer'];
  $footerPath ='/../theme/partials/customfooter.phtml';
}else{
  $footerPath ='/partials/footer.phtml';  
}
include __DIR__ . $footerPath;
?>
<script>
    $(document).ready(function(){
        $('.scrolling_tab').on('click', function(){
        var tabID = $(this).data('id');
        handleActiveTab(tabID)     
        $('.scrolling_tab').removeClass('active');
        $(this).toggleClass('active');
    })
    const currentUrl = window.location.href;
    const urlObj = new URL(currentUrl);
    const pathname = urlObj.pathname;
    const pathSegments = pathname.split('/');
    let endpoint = pathSegments[pathSegments.length - 1];
    if (endpoint && pathSegments.length > 2) {
        $('.scrolling_tab').removeClass('active');
        var activeTab = $('.scrolling_tab[data-id="' + endpoint + '"]');
        activeTab.addClass('active');
        handleActiveTab(endpoint);
    }
})

function leftScrollShopPage() {
  const left = document.querySelector(".cat_tab_slider2");
  left.scrollBy(200, 0);
}
function rightScrollShopPage() {
  const right = document.querySelector(".cat_tab_slider2");
  right.scrollBy(-200, 0);
}
function handleActiveTab(tabID){
    if (tabID != "0") {   
        $('.category_section').addClass('d-none');
    } else {
        $('.category_section').removeClass('d-none');
    }
    $('#' + tabID).removeClass('d-none');
}
</script>