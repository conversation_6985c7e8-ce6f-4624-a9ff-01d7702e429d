<style>
    body{
    background-color: #fff !important;
  }
  .product_detail_page_box {
    padding-left: 30px;
}
  span.detail-page-qty {
    color: #000;
  }
  section.related-product-section {
    padding: 70px 0px;
    display: block;
    width: 100%;
  }

  .product-category p {
    color: #000;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    text-transform: uppercase;
    margin-bottom: 8px;
  }

  .optionset_listing li {
    padding: 20px 5px;
    flex: 0 0 25%;
  }

  .btn-container-deatil {
    display: grid;
    grid-template-columns: 25fr 75fr;
    gap: 20px;
  }

  .btn-container-deatil {
    display: grid;
    grid-template-columns: 25fr 75fr;
    gap: 15px;
  }

  .optionset_listing li {}

  .product_detail_page_qty_btns i {
    font-size: 20px;
    font-weight: bolder;
    background: #000 !important;
    color: #fff;
    width: 34px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .product_detail_page_parent {
    margin: 30px 0px 40px 0px;
    padding: 0px 15px;
  }

  .product_detail_page_content {
    grid-template-columns: 50% 50%;
  }

  .detail_parent_img {
    margin-bottom: 24px;
  }

  .multi_img_list {
    margin: 0px -12px;
    overflow-x: auto;
    overflow-y: hidden;
    flex-wrap: nowrap;
  }

  .multi_img_list figure {
    padding: 0px 12px;
    flex: 0 0 20%;
    max-width: 20%;
  }

  .multi_img_list figure img {
    border-radius: 5px;
  }

  .multi_img_list figure.active img {
    border: 2px solid #286b53;
  }

  .detail_page_img_divider {
    border: 1px solid #0000001a;
    margin: 40px 0px;
  }

  /* review section css start */

  .overall_rating_box {
    border-bottom: 1px solid #00000026;
    padding-bottom: 15px;
    margin-bottom: 32px;
  }

  .overall_rating_box h3 {
    font-size: 20px;
    font-weight: 700;
    line-height: 30.24px;
    color: #000;
    margin: 0px;
  }

  .overall_rating_box_heading {
    padding-bottom: 10px;
  }

  .overall_rating_box_heading .write_review_btn {
    font-size: 11.66px;
    font-weight: 700;
    line-height: 17.5px;
    color: #286b53;
    text-decoration: underline;
  }

  .overall_rating_box small {
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
    text-transform: capitalize;
  }

  .overall_rating_box_detail h2 {
    font-size: 48px;
    font-weight: 600;
    line-height: 80px;
    padding-right: 18px;
    margin: 0px;
  }

  .overall_total_rating_stars {
    padding-bottom: 8px;
  }

  .overall_total_rating_stars i {
    padding-right: 4px;
    font-size: 14px;
    color: #ffb33e;
  }

  .overall_total_rating p {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
  }

  .detail_page_review_list_main strong {
    font-size: 24px;
    font-weight: 700;
    line-height: 30.24px;
    padding-bottom: 20px;
  }

  .detail_page_single_review {
    border-bottom: 1px solid #00000026;
    padding-bottom: 24px;
    margin-bottom: 24px;
    grid-template-columns: 1.2fr 3fr;
    gap: 20px 30px;
  }

  .detail_page_single_review figure img {
    margin-right: 10px;
    width: 48px;
    height: 48px;
  }

  .detail_page_single_review figure figcaption strong {
    font-size: 15px;
    font-weight: 600;
    line-height: 18.9px;
    color: #212121;
    padding-bottom: 3px;
  }

  .detail_page_single_review figure figcaption span {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
    color: #9fa19d;
  }

  .detail_page_single_review_stars {
    padding-bottom: 8px;
  }

  .detail_page_single_review_stars i {
    padding-right: 4px;
    font-size: 14px;
    color: #ffb33e;
  }

  .detail_page_single_review_data p {
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    color: #464646;
  }

  .load_more_review_btn a {
    background-color: var(--theme-bg-color);
    color: var(--theme-text-color) !important;
    font-size: 14px;
    font-weight: 400;
    line-height: 20.13px;
    border-radius: 10px;
    padding: 10px 55px;
    text-transform: capitalize;
  }

  /* review section css end */

  /* detail page content side css start */
  .product_heading {
    margin-bottom: 16px;
  }

  .product_heading strong {
    color: #000;
    font-size: 30px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }

  .product-description p {
    color: #000;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 24px;
  }

  .detail_review_section {
    margin-bottom: 22px;
  }

  .reviews_start_list i {
    color: #fece23;
    font-size: 14px;
    padding: 0px 2px;
  }

  .detail_review_section span {
    color: #52525b;
    font-size: 14px;
    font-weight: 500;
    line-height: 21px;
    padding-left: 8px;
  }

  .product_detail_prices {
    margin-bottom: 20px;
  }

  .product_detail_prices strong {
    font-size: 24px;
    font-weight: 700;
    line-height: 30.24px;
    color: #18181b;
    padding-right: 10px;
  }

  .product_detail_prices .text_decoration_line_through {
    font-size: 21px;
    text-decoration: line-through;
    color: #71717a;
    line-height: 26.46px;
  }

  .detail_page_divider {
    border: 1px solid #0000001a;
    margin-bottom: 20px;
  }

  .product_detail_prices span {
    font-size: 16px;
    line-height: 17px;
    color: #9b9bb4;
  }

  .product_condition {
    margin-bottom: 18px;
  }

  .product_condition span {
    font-size: 14px;
    font-weight: 400;
    line-height: 21px;
  }

  .product_condition span strong {
    font-weight: 600;
    padding-left: 8px;
  }

  .detail_product_colors {
    margin-bottom: 18px;
  }

  .detail_product_colors span {
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
    padding-bottom: 10px;
  }

  .single_color_box_main {
    width: 40px;
    height: 40px;
    margin-right: 12px;
    padding: 5px;
    border-radius: 4px;
  }

  .single_color_box {
    border-radius: 4px;
  }

  .single_color_box_main:last-child {
    margin-right: 0px;
  }

  .single_color_box_main.selected {
    border: 1px solid var(--theme-bg-color);
  }

  .product_detail_page_cart_btns {
    margin-bottom: 20px;
  }

  .detail_page_qty_header {
    margin-bottom: 12px;
  }

  .detail_page_qty_header strong {
    color: #000;
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
  }

  .detail_page_qty_header span {
    color: #286b53;
    font-size: 14px;
    font-weight: 500;
    line-height: 18px;
  }


  .product_detail_page_qty_btns .decrement_btn {
    left: 8px;
  }

  .product_detail_page_qty_btns .increment_btn {
    right: 8px;
  }

  .product_detail_page_qty_btns input {
    font-style: normal;
    font-weight: 600;
    font-size: 18px;
    color: #000000;
    text-align: center;
    border: 1px solid #cccccc;
    border-radius: 8px;
    padding: 15px 16px;
  }

 

  .detail_buy_btn {
    color: var(--theme-text-color) !important;
    background-color: var(--theme-bg-color);
  }

  /* detail page content side css end */

  /* accordian descrition css start */

  .detail_single_acc_main {
    margin-bottom: 20px;
    border-bottom: 1px solid #00000026;
  }

  .detail_single_acc_header {
    cursor: pointer;
    border-bottom: none;
    padding-bottom: 20px;
  }

  .detail_single_acc_header span.title {
    font-size: 16px;
    font-weight: 500;
    line-height: 20.16px;
    color: #000;
  }

  .detail_single_acc_header .acc_icon i {
    font-size: 16px;
  }

  .detail_single_acc_header:not(.collapsed) .rotate-icon {
    transform: rotate(180deg);
  }

  .detail_single_acc_data {
    gap: 10px;
    padding-bottom: 28px;
  }

  .detail_single_acc_data p {
    font-size: 12px;
    font-weight: 400;
    line-height: 25px;
    color: #000;
  }

  ul.detail_single_acc_data_listing {
    grid-template-columns: 1fr 1fr 1fr;
    gap: 24px 24px;
  }

  ul.detail_single_acc_data_listing li {
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
    color: #000;
  }

  ul.detail_single_acc_data_listing li i {
    color: #000;
    font-size: 6px;
    padding-right: 10px;
  }

  /* accordian descrition css end */

  /* related product section css start */
  .related_products {
    margin-bottom: 70px;
    padding: 0px 15px;
  }

  .related_products_header {
    padding-bottom: 9px;
  }

  .related_products_header h5 {

    font-size: 20px;
    font-weight: 700;
    line-height: 26.66px;
    color: #27292d;
  }

  .related_products_header a {
    font-size: 14px;
    font-weight: 500;
    line-height: 17.64px;
    color: #27292d;
  }

  /* related product section css end */

  @media (max-width: 991px) {
    .product_detail_page_parent {
      margin: 10px 0px 100px 0px;
    }

    .product_detail_page_figure_box {
      max-width: 540px;
      margin: 0 auto;
    }

    .detail_page_img_divider {
      border: 1px solid #0000001a;
      margin: 30px 0px 0px 0px;
    }

    .multi_img_list {
      margin: 0px -8px;
    }

    .multi_img_list figure {
      padding: 0px 8px;
      flex: 0 0 25%;
      max-width: 25%;
    }

    .product_detail_page_content {
      grid-template-columns: 100%;
      grid-template-areas: ".";
    }

    .product_detail_page_heading h2 {
      font-size: 20px;
      line-height: 25px;
    }

    .related_products {
      margin-bottom: 40px;
    }
  }

  @media (max-width: 768px) {
    .multi_img_list figure {
      flex: 0 0 33.333%;
      max-width: 33.333%;
    }

    .detail_page_single_review {
      grid-template-columns: 1fr;
    }

    .detail_page_single_review figure {
      align-items: center !important;
    }
  }

  @media (max-width: 540px) {
    .product_heading strong {
      font-size: 16px;
      line-height: 24px;
    }

    .product_heading {
      margin-bottom: 10px;
    }

    .product_detail_prices strong {
      font-size: 14px;
      line-height: 15.24px;
      padding-right: 5px;
    }

    .product_detail_prices .text_decoration_line_through {
      font-size: 14px;
      line-height: 15.24px;
    }

    .product_detail_prices span {
      font-size: 11px;
    }

    .single_color_box_main {
      width: 30px;
      height: 30px;
      padding: 3px;
    }

    .product_detail_page_qty_btns i {
      padding: 8px 8px;
      font-size: 12px;
    }

    .product_detail_page_qty_btns input {
      font-size: 13px;
      padding: 11px 10px;
    }

    .product_detail_page_qty_btns .increment_btn {
      right: 6px;
    }

    .product_detail_page_qty_btns .decrement_btn {
      left: 6px;
    }

  

    .product_detail_page_qty_btns {
      margin-bottom: 20px;
    }

    .detail_single_acc_header span.title {
      font-size: 14px;
    }

    .detail_single_acc_header .acc_icon i {
      font-size: 14px;
    }

    .overall_rating_box h3 {
      font-size: 16px;
      line-height: 21.24px;
    }

    .overall_rating_box_detail h2 {
      font-size: 38px;
      line-height: 60px;
    }

    .overall_total_rating_stars {
      padding-bottom: 6px;
    }

    .overall_total_rating_stars i {
      font-size: 11px;
    }

    .overall_rating_box small {
      padding-bottom: 10px;
    }

    .overall_total_rating p {
      font-size: 11px;
    }

    .overall_rating_box {
      margin-bottom: 20px;
    }

    .detail_page_review_list_main strong {
      font-size: 16px;
      line-height: 20.24px;
    }

    ul.detail_single_acc_data_listing {
      grid-template-columns: 1fr 1fr;
      gap: 20px 20px;
    }

    .log_des_rating_num_detail h2 {
      font-size: 44px;
      line-height: 70px;
      padding-right: 15px;
    }

    .detail_page_single_review_data p {
      font-size: 12px;
      line-height: 19px;
    }

    .detail_page_single_review figure figcaption strong {
      font-size: 12px;
      line-height: 13.9px;
    }

    .detail_page_single_review figure figcaption span {
      font-size: 12px;
      line-height: 15px;
    }

    .detail_page_single_review figure img {
      width: 40px;
      height: 40px;
    }

    .load_more_review_btn a {
      font-size: 12px;

      line-height: 18.13px;

      padding: 5px 26px;
    }
  }




  /* Cart bill Summary section start */
  .summary_box {
    position: sticky;
    top: 0px;
  }

  .price_detail_box {
    background-color: #fff;
    padding: 20px 20px;
    border-radius: 6px;
    margin-bottom: 20px;
  }

  .order_listing_header span.title {
    font-size: 18px;
    font-weight: 700;
    line-height: 22.68px;
    color: #000;
  }

  .order_listing_header .acc_icon i {
    font-size: 14px;
    color: #000;
  }

  .order_listing_header:not(.collapsed) .rotate-icon {
    transform: rotate(180deg);
  }

  .order_detail_list {
    padding-top: 20px;
  }

  .order_detail_single_list figure {
    min-width: 48px;
    max-width: 50px;
    height: 48px;
    border-radius: 5px;
    margin-right: 14px;
  }

  .order_detail_single_list span {
    width: 20px;
    height: 20px;
    color: var(--theme-text-color) !important;
    background-color: var(--theme-bg-color);
    border-radius: 50%;
    font-weight: 500;
    font-size: 11px;
    line-height: 13px;
    top: -4px;
    right: -11px;
  }

  .multiple_images_box img {
    object-fit: cover !important;
    height: 150px;
    padding: 0px 10px 15px 10px;
  } 
  .order_detail_single_list_name p {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 18px;
    color: #000;
    display: -webkit-box !important;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .order_detail_single_list_price {
    font-weight: 700;
    font-size: 14px;
    line-height: 17px;
    color: #000;
    padding-left: 10px;
  }

  .order_detail_single_list_price small {
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 15px;
    color: #000;
    padding-right: 4px;
  }

  .bill_box_heading {
    font-size: 18px;
    font-weight: 700;
    line-height: 22.68px;
    color: #000;
    padding-bottom: 20px;
  }

  .total_bill_detail {
    gap: 14px;
    padding-bottom: 20px;
  }

  .total_bill_single_list p {
    font-size: 14px;
    font-weight: 400;
    line-height: 17.64px;
    color: #000;
  }

  .total_bill_single_list small {
    color: #00000080;
    padding-left: 5px;
  }

  .total_bill_single_list span {
    font-size: 14px;
    font-weight: 700;
    line-height: 17.64px;
  }

  .summary_discount_header span.title {
    font-size: 14px;
    font-weight: 600;
    line-height: 18px;
    color: #35a1de;
  }

  .summary_discount_header .acc_icon i {
    font-size: 14px;
    color: #35a1de;
  }

  .summary_discount_header:not(.collapsed) .rotate-icon {
    transform: rotate(180deg);
  }

  .summary_divider {
    border: 1px solid #f2f2f2;
    margin: 20px 0px;
  }

  .total_bill_final {
    gap: 14px;
  }

  .checkout_order_btn a {
    padding: 15px 10px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    line-height: 18px;
    color: var(--theme-text-color) !important;
    background-color: var(--theme-bg-color);
  }

  .discount_Coupon_input {
    margin-top: 20px;
  }

  .discount_Coupon_input_field input {
    background: #fff;
    border: 1px solid #0000001f;
    border-radius: 4px;
    height: 44px;
    font-size: 12px;
    font-weight: 400;
    line-height: 15.12px;
    color: #00000080;
    padding-left: 15px;
    padding-right: 80px;
  }

  .discount_Coupon_input_field label.error {
    padding-top: 10px;
    color: red;
    font-size: 12px;
  }

  .discount_Coupon_btn {
    height: 44px;
    width: 74px;
    top: 0px;
    right: 0px;
    color: var(--theme-text-color) !important;
    background-color: var(--theme-bg-color);
    border-radius: 4px;
  }

  .discount_Coupon_btn a {
    font-size: 12px;
    font-weight: 400;
    line-height: 15.12px;
    color: var(--theme-text-color) !important;
  }

  @media (max-width: 768px) {
    .order_listing_header span.title {
      font-size: 16px;
      line-height: 20.68px;
    }

    .bill_box_heading {
      font-size: 16px;
      line-height: 20.68px;
    }

    .order_detail_single_list_price {
      font-size: 12px;
    }

    .total_bill_single_list p {
      font-size: 12px;
    }

    .total_bill_single_list span {
      font-size: 12px;
    }
  }

  /* Cart bill Summary section End */

  /* checkout header style */

  .checkout_web_header {
    background: #38393d;
    box-shadow: 0px 4px 14px 0px #00000014;
    padding: 22px 10px;
  }

  .checkout_web_header_list figure a {
    width: 132px;
  }

  .checkout_header_cart_detail i {
    color: #fff;
    font-size: 16px;
    line-height: 24px;
  }

  .product_detail_page_box_extras {
    margin-bottom: 20px;
  }

  .checkout_header_cart_detail h5 span {
    color: #35a1de;
    padding-left: 3px;
  }

  @media (max-width: 540px) {
    section.related-product-section {
      padding: 30px 0px;
      display: block;
      width: 100%;
    }


    .checkout_web_header_list figure a {
      width: 100px;
    }

    .checkout_web_header {
      padding: 15px 10px;
    }

    .checkout_header_cart_detail i {
      font-size: 12px;
      line-height: 15px;
    }

    .checkout_header_cart_detail h5 {
      font-size: 12px;
      line-height: 15px;
      padding-left: 4px;
    }
  }

  /* checkout header style end */


  .payment_type_list {
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    padding-bottom: 20px;
  }



  @media (max-width: 991px) {
    .checkout_page_parent {
      grid-template-columns: 1fr;
      gap: 20px 20px;
    }
  }


</style>
<?php
$headerPath = (isset($businessDetail['web_theme']) && $businessDetail['web_theme'] != "") ? '/../theme/partials/customHeader.phtml' : '/partials/header.phtml';
include __DIR__ . $headerPath;
?>
<?php


$itemPrice = $data['price'];
$productDesc = $data['desc'];
$shortDesc = '';
$longDesc = '';
if (preg_match('/<short_desc>(.*?)<\/short_desc>/s', $productDesc, $matches)) {
  $shortDesc = $matches[1];
}
if (preg_match('/<long_desc>(.*?)<\/long_desc>/s', $productDesc, $matches)) {
  $longDesc = $matches[1];

}
$productCategory = $data['category'];

$discountPrice = isset($data['discount_value']) && is_numeric($data['discount_value']) ? (float)$data['discount_value'] : 0;
$finalPrice = $itemPrice - $discountPrice;
$discountdisplay = $data['discount_value'] != 0 && $data['discount_value'] != "" ? $data['price'] : "";
$currency = getCurrencySymbol($data['currency']);
$resturent_details = $this->view->businessDetail;
$theme_setting = json_decode($resturent_details['theme_settings']);
include __DIR__ . '/./partials/schema.phtml';
?>
<div class="res_sidebar offcanvas offcanvas-end" id="navbarOffcanvas" tabindex="-1" aria-labelledby="offcanvasNavbarLabel">
  <div class="d-flex w-100 justify-content-end">
    <a class="sidebar_close_btn" data-bs-dismiss="offcanvas" aria-label="Close"><i class="fas fa-times"></i></a>
  </div>
  <div class="offcanvas-body">
    <div class="res_header_links_list d-flex flex-column align-items-start w-100">
      <a href="index.html">Home</a>
      <a href="shop.html">Shop</a>
      <a>About Us</a>
      <a>Contact Us</a>
      <a href="shop.html">Products </a>
      <a href="shop.html">Services </a>
    </div>
  </div>
</div>

<div class="breadcrumb_parent w-100 d-block d-none">
  <div class="custom_container">
    <ol class="breadcrumb_list d-flex align-items-center w-100 flex-wrap">
      <li class="d-flex align-items-center">
        <a href="index.html">Home</a>
      </li>
      <li class="active d-flex align-items-center">
        <a href="shop.html"><i class="far fa-chevron-right"></i> Products</a>
      </li>
    </ol>
  </div>
</div>

<div class="product_detail_page_parent d-inline-block w-100">
  <?php
  if (isset($data) && is_array($data) && count(array_filter($data)) > 0) {
    ?>
    <section class="product_detail_page_section">
      <div class="custom_container">
        <div class="product_detail_page_content d-grid w-100">
          <div>
            <div class="product_detail_page_figure_box d-flex flex-column w-100">
              <?php if (count($data['images']) > 1): ?>
                <!-- Multiple images case -->
                <div class="slider slider-for mb-4">
                  <?php foreach ($data['images'] as $index => $image): ?>
                    <div>
                      <img width="100%" height="600px" src="<?= $image['image'] ?>" class="object-fit-cover" alt="<?= $title ?>"
                        style="margin-right: 10px;" />
                    </div>
                  <?php endforeach; ?>
                </div>

                <div class="multiple_images_box slider slider-nav">
                  <?php foreach ($data['images'] as $index => $image): ?>
                    <div>
                      <img style="cursor:pointer" data-slide="<?= $index ?>" src="<?= $image['image'] ?>" width="150px"
                        class="object-fit-cover clickable-thumbnail" alt="<?= $title ?>" />
                    </div>
                  <?php endforeach; ?>
                </div>
              <?php else: ?>
                <!-- Single image case -->
                <figure class="detail_parent_img position-relative w-100">
                  <img src="<?= (isset($data['image']) && basename($data['image']) != "no_image.jpg") ? $data['image'] : (isset($businessDetail['logo_image']) ? $businessDetail['logo_image'] : '') ?>" class="w-100 object-fit-cover" alt="<?= $title ?>"
                    style="margin-right: 10px;" />
                </figure>
              <?php endif; ?>
            </div>
          </div>

          <div class="product_detail_page_box d-flex flex-column w-100">
            <div class="product-category">
              <p><?= $productCategory ?></p>
            </div>
            <div class="product_heading">
              <strong><?= $data['name'] ?></strong>
            </div>
            <div class="product-description">
              <?= isset($shortDesc) && $shortDesc != "" ? $shortDesc : $data['desc'] ?>
            </div>
            <div class="product_detail_prices d-flex align-items-center flex-wrap want?">
            <strong class="detail_prices">
                <?php if (isset($finalPrice) && $finalPrice > 0): ?>
                    <strong class="currency"><?= $currency ?></strong>
                    <strong class="amount"><?= formatPrice($finalPrice) ?></strong>
                <?php endif; ?>
            </strong>
              <input type="hidden" id="finalDetailPagePrice" value="<?= $finalPrice ?>" data-currency="<?= $currency ?>">
              <?= !empty($discountdisplay) ? '<strong class="text_decoration_line_through">' . $currency . " " . formatPrice($discountdisplay) . '</strong>' : '' ?>
              <?php echo isset($data['discount']) && $data['discount'] != "" ? "<span>" . $data['discount'] . "% OFF</span>" : "" ?>
            </div>

            <div class="product_detail_page_box_extras">
              <?php
              echo isset($data['weight_value']) && $data['weight_value'] != ''
                ? '<div class="detail_page_box_extras_list">
                    <strong style="margin-right:20px">Weight:</strong>
                    <span>' . $data['weight_value'] . ' ' . $data['weight_unit'] . '</span>
                  </div>'
                : "";
              ?>

            </div>

            <div class="detail_page_divider d-flex w-100"></div>


            <?php if (isset($cart_feature_exsits) && isset($cart_btn_status) && $cart_feature_exsits == "cart" && $cart_btn_status == "show") { ?>
              <div class="product_detail_page_cart_btns d-flex flex-column w-100">
                <div class="optionset">
                  <?php
                  foreach ($data['options'] as $index => $element) {
                    echo renderAccordionElement($element, $index , $currency);
                  }
                  ?>
                </div>
              </div>
                  <?php
                  if(isset($data['allow_note']) && $data['allow_note'] == 1){
                    ?>
                    <div class="form-group" style="padding-left: 0px !important;">
                  <label class="popup-label" style="margin-top:.5rem;font-weight:bold;">Special Instructions</label>
                  <textarea  style="margin-right: 5px;margin-top: 5px;"
                  id="<?php echo "special-{$data['menu_item_id']}"; ?>"
                      data-name=""
                      class="form-control comment my-3"
                      type="textbox"
                      name="comment"></textarea>
                </div>
                    <?php

                  }
                  ?>
            <?php } ?>
            <?php
            $jsonData = json_encode($data);
            $encodedData = base64_encode($jsonData);
            echo "<input type='hidden' id='" . $data['menu_item_id'] . "_product' class='hidden_field product_detail' value='" . htmlspecialchars($encodedData, ENT_QUOTES, 'UTF-8') . "'>";
            ?>

            <input type="hidden" class="currency" value="<?php echo $currency ?>">
            <?php if (isset($data) && isset($data['status']) && $data['status'] == '0') { ?>
              <?php if ($cart_feature_exsits == "cart" && $cart_btn_status == "show") { ?>
                <div class="btn-container-deatil">
                  <div
                    class="product_detail_page_qty_btns d-flex justify-content-between align-items-center position-relative">
                    <i class="far fa-minus decrement_btn"></i>
                    <span class="detail-page-qty" id="<?php echo $data['menu_item_id'] ?>_qty" >1</span>
                    <i class="far fa-plus increment_btn"></i>
                  </div>
                  <div class="detail_page_add_to_cart_btn d-flex flex-column w-100">
                    <a class="detail_add_cart_btn detail-page-add-btn text-center detail-add-to-cart d-flex align-item-center justify-content-center"
                      id="<?= $data['menu_item_id'] ?>"><span class="cartBtn cart-text">Add to cart</span> <div style="width:22px !important;height:22px !important" class='spinner-border loader_popup hide' role='status'> <span class='sr-only'>Loading...</span></div></a>
                  </div>
                </div>
              <?php } ?>
            <?php } else { ?>
              <p class='out_of_stock'><?php
              echo isset($theme_setting->theme_settings->product_availability_text) && $theme_setting->theme_settings->product_availability_text != "" ? $theme_setting->theme_settings->product_availability_text : "Out of Stock"
                ?></p> <?php } ?>

          </div>


        </div>


      </div>
  </div>

  <?php
  if (isset($longDesc) && $longDesc != "") {
    echo $longDesc;
  }
  ?>
  </section>
  <?php
  } else {
    include __DIR__ . '/./partials/404Page.phtml';
  } ?>
<?php
if (isset($data['suggestions']) && !empty($data['suggestions'])) { 
  include __DIR__ .'/./partials/suggesstions.phtml';
}
  ?>

</div>

<?php

function renderNumberOptionSet($element, $index, $optionsHtml, $isOptional, $innerOptionCount, $outerOptionsetId)
{
  return '
    <div class="single_optionset d-flex flex-column">
        <h2 class="optionset_header d-flex flex-column" id="optionsetheading' . $index . '">
            <button class="optionset_header_btn accordion-button d-flex align-items-center justify-content-between"
                type="button" data-bs-toggle="collapse" data-bs-target="#optionsetCollapse' . $index . '"
                aria-expanded="false" aria-controls="optionsetCollapse' . $index . '">
                <div class="optionset_header_data d-flex flex-column">
                    <span>' . $element['name'] . '</span>
                    <div class="d-flex align-items-center">
                    <small class="small_validation" name="' . $element['name'] . '" id="' . $element['id'] . '">' . $isOptional . '&nbsp</small>
                    <label name="' . $element['name'] . '" id="' . $element['id'] . '" class="lable_validation"></label>
                    </div>
                </div>
                <i class="fas fa-chevron-down ms-auto"></i>
            </button>
        </h2>
        <div id="optionsetCollapse' . $index . '" class="accordion-collapse collapse show"
            aria-labelledby="optionsetheading' . $index . '" data-bs-parent="#optionsetAccordion">
            <ul class="optionset_listing d-flex">
                ' . implode("", $optionsHtml) . '
            </ul>
            ' . ($innerOptionCount > 0 ? '
            <div class="inner_options_container d-flex flex-column inner_option_parent" id="innerOptionsContainer_' . $outerOptionsetId . '">
                <div class="inner_optionset_listing d-flex" id="inner_option_input"></div>
            </div>' : "") . '
        </div>
    </div>';
}

function renderOtherOptionSet($element, $index, $optionsHtml, $isOptional, $innerOptionCount, $outerOptionsetId)
{
  return '
    <div class="single_optionset d-flex flex-column">
        <h2 class="optionset_header d-flex flex-column" id="optionsetheading' . $index . '">
            <button class="optionset_header_btn accordion-button d-flex align-items-center justify-content-between"
                type="button" data-bs-toggle="collapse" data-bs-target="#optionsetCollapse' . $index . '"
                aria-expanded="false" aria-controls="optionsetCollapse' . $index . '">
                <div class="optionset_header_data d-flex flex-column">
                    <span>' . $element['name'] . '</span>
                    <div class="d-flex align-items-center">
                    <small class="small_validation" name="' . $element['name'] . '" id="' . $element['id'] . '">' . $isOptional . '&nbsp</small>
                    <label name="' . $element['name'] . '" id="' . $element['id'] . '" class="lable_validation"></label>
                    </div>
                </div>
                <i class="fas fa-chevron-down ms-auto"></i>
            </button>
        </h2>
        <div id="optionsetCollapse' . $index . '" class="accordion-collapse collapse show"
            aria-labelledby="optionsetheading' . $index . '" data-bs-parent="#optionsetAccordion">
            <ul class="optionset_listing d-flex">
                ' . implode("", $optionsHtml) . '
            </ul>
            ' . ($innerOptionCount > 0 ? '
            <div class="inner_options_container d-flex flex-column inner_option_parent" id="innerOptionsContainer_' . $outerOptionsetId . '">
                <div class="inner_optionset_listing d-flex" id="inner_option_input"></div>
            </div>' : "") . '
        </div>
    </div>';
}

function renderNoOptionsAvailable($element, $index, $isOptional)
{
  return '
    <div class="single_optionset d-flex flex-column">
        <h2 class="optionset_header" id="optionsetheading' . $index . '">
            <button class="optionset_header_btn accordion-button d-flex align-items-center justify-content-between"
                type="button" data-bs-toggle="collapse" data-bs-target="#optionsetCollapse' . $index . '"
                aria-expanded="false" aria-controls="optionsetCollapse' . $index . '">
                <div class="optionset_header_data">
                    <span>' . $element['name'] . '</span>
                    <small>(' . $isOptional . ')</small>
                </div>
                <i class="fas fa-chevron-down ms
                <i class="fas fa-chevron-down ms-auto"></i>
            </button>
        </h2>
        <div id="optionsetCollapse' . $index . '" class="accordion-collapse collapse show"
            aria-labelledby="optionsetheading' . $index . '" data-bs-parent="#optionsetAccordion">
            <p>No options available</p>
        </div>
    </div>';
}

function renderOptionsHtml($element, $inputType , $currency)
{
  return array_map(function ($option) use ($element, $inputType , $currency) {
    $imageHtml = (isset($option['image']) && $option['image'] != "https://static.tossdown.com/images/") ? '<figure class="w-100"><img class="w-100 h-100 object-fit-contain" src="' . $option['image'] . '" /></figure>' : "";
    $priceHtml = (!empty($option['price']) && $option['price'] != "0.00") ? (isset($currency) ? $currency : "")  . " ". formatPrice($option['price']) : "";
    if ($inputType === "number") {
      return '
            <li>
                <div class="option_set_values counter_parent">
                    ' . $imageHtml . '
                    <small>' . $option['name'] . '</small>
                    <span>' . $priceHtml . '</span>
                    <div class="counter product_cart_btn">
                        <a class="counter-btn minus qty_increased">-</a>
                        <input data-qty-min="' . (isset($element['min_quantity']) && $element['min_quantity'] != "" ? $element['min_quantity'] : 0) . '" type="checkbox" data-qty="' . $element['quantity'] . '" class="option_set_validation detail-page-options" name="' . $element['name'] . '" data-id="' . $option['id'] . '" value="' . $option['name'] . ',' . $option['price'] . '" data-qty-current="0">
                        <span class="counter-input">0</span>
                        <a class="counter-btn plus qty_increased">+</a>
                    </div>
                </div>
            </li>';
    } else {
      return '
            <li>
                <label class="option_set_values">
                    <input data-qty-min="' . $element['min_quantity'] . '" type="' . $inputType . '" data-qty="' . $element['quantity'] . '" class="option_set_validation detail-page-options" name="' . $element['name'] . '" data-id="' . $option['id'] . '" value="' . $option['name'] . ',' . $option['price'] . '">
                    ' . $imageHtml . '
                    <small>' . $option['name'] . '</small>
                    <span>' . $priceHtml . '</span>
                </label>
            </li>';
    }
  }, $element['items']);
}

function renderAccordionElement($element, $index , $currency)
{
  if (isset($element['items']) && is_array($element['items'])) {
    $inputType = ($element['min_quantity'] == 1 && $element['quantity'] == 1) ? "radio" : (($element['min_quantity'] == 0 && $element['quantity'] > 1) ? "number" : "checkbox");
    $isOptional = ($element['quantity'] == 0) ? "Optional" : "Required";
    $outerOptionsetId = $element['id'];
    $innerOptionCount = array_reduce($element['items'], function ($count, $option) {
      return $count + (isset($option['items']) ? count($option['items']) : 0);
    }, 0);

    $optionsHtml = renderOptionsHtml($element, $inputType , $currency);

    if ($inputType === "number") {
      return renderNumberOptionSet($element, $index, $optionsHtml, $isOptional, $innerOptionCount, $outerOptionsetId);
    } else {
      return renderOtherOptionSet($element, $index, $optionsHtml, $isOptional, $innerOptionCount, $outerOptionsetId);
    }
  } else {
    $isOptional = ($element['quantity'] === 0) ? "Optional" : "Required";
    return renderNoOptionsAvailable($element, $index, $isOptional);
  }
}
 ?>
<script>
  $(document).ready(function () {
    let productdetails = <?= json_encode($data) ?>;
    viewEventDataLayer(productdetails);

    function viewEventDataLayer(productdetails){
      window.dataLayer = window.dataLayer || [];
      window.dataLayer.push({
        event: "viewItem",
        ecommerce: {
          detail: {
            products: 
              {
                item_id: productdetails.menu_cat_id,          
                item_name: productdetails.name,   
                item_category: productdetails.category,   
                price: productdetails.price,                 
                currency: productdetails.currency,             
              }

          } 
        }
      });
    }
  })

</script>




<?php
$headerPath = (isset($web_theme) && !empty($web_theme)) ? '/../theme/partials/customfooter.phtml' : '/partials/footer.phtml';
include __DIR__ . $headerPath;
?>
<?php include __DIR__ . '/partials/cartSummary.phtml'; ?>