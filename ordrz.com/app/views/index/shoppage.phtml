<style>
  .filter_btn_dismiss{
    display: none;
  }
  @media (max-width:992px) {
    .filter_parent .filters_list {
        position: fixed;
        width: 100%;
        background: #fff;
        z-index: 999;
        left: 0px;
        padding: 20px;
        overflow: scroll;
        height: 70vh;
        padding-top: 15px;
        box-shadow: 0px 10px 30px rgba(0, 0, 0, 1);
        bottom: -70vh;
        transition: 0.5s;
    }
    .filter_btn_dismiss{
      display: block;
      font-size: 35px;
      color: black;
      font-weight: bold;
      float: right;
      margin-right: 20px;
    }
}

</style>

<?php
$headerPath = (isset($businessDetail['web_theme']) && $businessDetail['web_theme'] != "") ? '/../theme/partials/customHeader.phtml' : '/partials/header.phtml';
include __DIR__ . $headerPath;
?>
<?php include __DIR__ . '/partials/cartSummary.phtml'; ?>

<!-- <div class="breadcrumb_parent w-100 d-inline-block ">
      <div class="custom_container">
        <ol
          class="breadcrumb_list d-flex align-items-center w-100 flex-wrap p-0 m-0">
          <li class="d-flex align-items-center">
            <a href="index.html">Home</a>
          </li>
          <li class="active d-flex align-items-center">
            <a href="#"><small>/</small> Products</a>
          </li>
          <li class="active d-flex align-items-center">
            <a href="detail.html"><small>/</small>
              Detail</a>
          </li>
        </ol>
      </div>
    </div> -->

<div class="shop_page_parent w-100 d-inline-block">
  <div class="custom_container">
    <div class="shop_content_body d-flex">
      <div class="filter_parent d-flex flex-column">
        <div class="filters_list  d-lg-block" data-visible="false">
          <a class="filter_btn_dismiss" aria-label="Close"><i class="fas fa-times"></i></a>
          <div class="accordion filters_accordion_parent d-grid w-100" id="filtersAccordion">
            <div class="single_filter_section d-grid w-100">

              <h2 class="filter_header" id="filterheadingOne">
                <!-- <button class="filter_header_btn accordion-button d-flex align-items-center justify-content-between" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapseOne" aria-expanded="false" aria-controls="filterCollapseOne"> -->
                <button class="filter_header_btn">
                  <h5>Categories</h5>
                  <!-- <i class="fas fa-chevron-down ms-auto d-flex align-items-center justify-content-center"></i> -->
                </button>
              </h2>
              <div id="filterCollapseOne" class="accordion-collapse collapse show" aria-labelledby="filterheadingOne" data-bs-parent="#filtersAccordion">
                <div class="filter_search d-flex w-100 position-relative">
                  <i class="fas fa-search position-absolute top-0 d-flex h-100 align-items-center"></i>
                  <input type="text" placeholder="Find a category" data-type="cat" class="searchcategory search_input w-100 h-100 d-flex">
                </div>
                <ul class="filters_listing d-flex flex-column w-100 align-items-start category_section overflow-y-auto">
                  <?php
                  foreach ($categories as $category) {
                    if (isset($category['item_count']) && $category['item_count'] != 0) { ?>
                      <li class="align-items-center flex-wrap justify-content-between w-100 search-parent-cat" style='display:flex'>
                        <div class="d-flex align-items-center flex-wrap">
                          <div class="filter_checkbox d-flex">
                            <input type="checkbox" class="filter-checkbox" name="cat" data-id=<?= $category['category_id'] ?>  id="input<?= $category['category_id'] ?>" />
                            <label for="input<?= $category['category_id'] ?>"> </label>
                          </div>
                          <div id='<?= $category['category_id'] ?>' class="filter_list_detail d-flex align-items-center">
                            <span class='filter-cat filter-items'
                                  data-id="<?= $category['category_id'] ?>"
                                  data-slug="<?= isset($category['slug']) ? $category['slug'] : '' ?>"><?= $category['category_name'] ?></span>
                          </div>
                        </div>
                        <small><?= $category['item_count'] ?></small>
                      </li>
                  <?php }
                  } ?>
                  <div class="not-available-cat">
                  </div>
                </ul>
              </div>
            </div>
            <?php if (isset($brands) && count($brands) > 0) { ?>
              <div class="single_filter_section d-grid w-100 ">
                <h2 class="filter_header" id="filterheadingTwo">
                  <!-- <button class="filter_header_btn accordion-button d-flex align-items-center justify-content-between" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapseTwo" aria-expanded="false" aria-controls="filterCollapseTwo"> -->
                  <button class="filter_header_btn">
                    <h5>Brands</h5>
                    <!-- <i class="fas fa-chevron-down ms-auto d-flex align-items-center justify-content-center"></i> -->
                  </button>
                </h2>
                <div id="filterCollapseTwo" class="accordion-collapse collapse show" aria-labelledby="filterheadingTwo" data-bs-parent="#filtersAccordion">
                  <div class="filter_search d-flex w-100 position-relative">
                    <i class="fas fa-search position-absolute top-0 d-flex h-100 align-items-center"></i>
                    <input type="text" placeholder="Find a brand" data-type="brand" class="searchbrand search_input w-100 h-100 d-flex">
                  </div>
                  <ul class="filters_listing d-flex flex-column w-100 align-items-start overflow-y-auto brand_section">
                    <?php
                    foreach ($brands as $brand) {
                      if (isset($brand['total_items']) && $brand['total_items'] != 0) {
                    ?>
                        <li class="d-flex align-items-center flex-wrap justify-content-between w-100 search-parent-brand" style='display:flex'>
                          <div class="d-flex align-items-center flex-wrap">
                            <div class="filter_checkbox d-flex">
                              <input type="checkbox" class="filter-checkbox" name="brand" data-id=<?= $brand['id'] ?> id="input<?= $brand['id'] ?>" />
                              <label for="input<?= $brand['id'] ?>"> </label>
                            </div>
                            <div class="filter_list_detail d-flex align-items-center" id="<?= $brand['id'] ?>">
                              <span class="filter-brand filter-items" data-id="<?= $brand['id'] ?>"><?= $brand['name'] ?></span>
                            </div>
                          </div>
                          <small><?= $brand['total_items'] ?></small>
                        </li>
                    <?php  }
                    } ?>
                    <div class="not-available-brand">

                    </div>
                  </ul>
                </div>
              </div>
            <?php } ?>

            <div class="single_filter_section d-grid w-100">
              <h2 class="filter_header" id="filterheadingThree">
                <button class="filter_header_btn">
                  <h5>Price</h5>
                  <!-- <i class="fas fa-chevron-down ms-auto d-flex align-items-center justify-content-center"></i> -->
                </button>
              </h2>
              <div id="filterCollapseThree" class="accordion-collapse collapse show" aria-labelledby="filterheadingThree" data-bs-parent="#filtersAccordion">

                <!-- <div class="price_range_slider w-100 d-flex flex-column">
                  <div id="filter_slider_range" class="price_filter_range_bar"></div>

                  <p class="filter_range_value d-flex align-items-center">
                    <span class="price_curreny"></span>
                    <input type="text" id="amount" readonly />
                  </p>
                </div> -->

                <div class="d-flex flex-md-row align-items-center">
                  <div class="form-group me-2 mb-2 position-relative">
                    <input type="number" class="form-control" id="minPrice" name="minPrice" placeholder="Min">
                  </div>
                  <div class="d-flex align-items-center mb-2">
                    <span class="mx-2">-</span>
                  </div>
                  <div class="form-group mx-2 mb-2 position-relative">
                    <input type="number" class="form-control" id="maxPrice" name="maxPrice" placeholder="Max">
                  </div>
                  <div class="d-flex align-items-center mb-2">
                    <button type="button" id="price_filter" class="btn btn-dark mb-0">Apply</button>
                  </div>
                </div>

              </div>
            </div>

          </div>
        </div>
      </div>
      <div class="product_list_parent d-flex flex-column">
        <!-- <div class="shop_sort_filter_parent d-flex align-items-center justify-content-between w-100 flex-wrap">
          <h5 class="d-lg-block d-none">Search Results (52)</h5> -->

          <div class="res_filter_btn d-flex align-items-center d-lg-none" type="button">Filters <i class="fal fa-chevron-down"></i></div>

          <!--   <div class="shop_sort_filter d-flex align-items-center">
           <label type="button" id="sort_toggle" data-bs-toggle="dropdown" aria-expanded="false">Sort by <i class="fal fa-chevron-down"></i></label>
            <div class="sort_dropdown_main dropdown-menu" aria-labelledby="sort_toggle">
              <ul class="sort_dropdown d-grid w-10 0">
                <li class="w-100"><label class="sort_custom_radio position-relative w-100">Default
                    <input type="radio" checked="checked" name="radio">
                    <span class="checkmark"></span>
                  </label></li>
                <li class="w-100"><label class="sort_custom_radio position-relative w-100">Price:
                    Low to
                    High
                    <input type="radio" name="radio">
                    <span class="checkmark"></span>
                  </label></li>

                <li class="w-100"><label class="sort_custom_radio position-relative w-100">Price:
                    High to Low
                    <input type="radio" name="radio">
                    <span class="checkmark"></span>
                  </label></li>

                <li class="w-100"><label class="sort_custom_radio position-relative w-100">Newest
                    <input type="radio" name="radio">
                    <span class="checkmark"></span>
                  </label></li>
                <li class="w-100"><label class="sort_custom_radio position-relative w-100">Most
                    Popular
                    <input type="radio" name="radio">
                    <span class="checkmark"></span>
                  </label></li>

              </ul>
            </div>

          </div>
        </div> -->
        <div class="product_listing_box d-flex flex-column w-100">
          <div class="selected_filters_list d-flex flex-wrap w-100">
          <div class="selected_filter show_selected_filters d-flex align-items-center">
            </div>

         <?php if($_SERVER['REQUEST_URI'] != "/menu" && $_SERVER['REQUEST_URI'] != "/shop"){ ?>
          <div class="selected_filter d-flex align-items-center reset_btn" style="display:none !important"><small>Reset</small></div>
         <?php } ?>


            <?php
            $displayed_categories = [];
            if (isset($items) && is_array($items) && count(array_filter($items)) > 0) {
              $searchResult = (isset($web_theme) && isset($web_theme['card']['search_results'])) ? $web_theme['card']['search_results'] : '';
              $cardsToShow = (isset($searchResult) && isset($searchResult['settings']['cards_to_show'])) ? $searchResult['settings']['cards_to_show'] : '';
              $product_card_path = (isset($web_theme) &&  !empty($web_theme)) ? '/../theme/partials/customCards.phtml' : '/partials/productCard.phtml';
              foreach ($items as $item) {
                if ($item) {
                  $categoryId = $item['menu_cat_id'];
                  if (!in_array($categoryId, $displayed_categories)) {
                    $displayed_categories[] = $categoryId;
                    if (!empty($displayed_categories)) {
                      echo '</div></div>';
                    }
                    echo '<div class="product_list_heading d-flex justify-content-start">';
                    echo '<h6>' . $item['category'] . '</h6>';
                    echo '</div>';
                    echo '<div class="d-flex flex-column">';
                    echo '<div class="shop_product_listing_section row">';
                  }

                  $image_name = explode('/', $item['image']);
                  $product_name = end($image_name);
                  $item_name = strtolower($item['name']);
                  $delimiter = "-";
                  $slug = strtolower(trim(preg_replace('/[\s-]+/', $delimiter, preg_replace('/[^A-Za-z0-9-]+/', $delimiter, preg_replace('/[&]/', '', preg_replace('/[\']/', '', iconv('UTF-8', 'ASCII//TRANSLIT', $item_name))))), $delimiter));
                  $slugWithId = $slug . '-' . $item['menu_item_id'];

                  include __DIR__ . $product_card_path;

            ?>

            <?php
                }
              }
            // Close the last category divs
            if (!empty($displayed_categories)) {
              echo '</div></div>';
            }
            ?>

          </div>

        </div>
        <?php
         } else{
              include __DIR__ . '/./partials/404Page.phtml';
            }?>
      </div>
      <?php
    if($themeSettings['theme_settings']['menu_pagination_on_off'] == 1 && isset($items) && is_array($items) && count(array_filter($items)) > 0) {
           include __DIR__ . '/partials/pagination.phtml';
      }
    ?>
    </div>
  </div>
</div>

<!-- responsive filters -->



</body>
<?php
if (isset($web_theme) &&  !empty($web_theme)) {
  $theme['footer'] = $web_theme['footer'];
  $footerPath ='/../theme/partials/customfooter.phtml';
}else{
  $footerPath ='/partials/footer.phtml';
}
include __DIR__ . $footerPath;
?>
<script>
  $(document).on('input', '.searchcategory, .searchbrand', function() {
    var filter = $(this).val().toUpperCase();
    let noResultFound = true;
    var searchType = $(this).data('type');
    $(".not-available-" + searchType).html(' ');
    $(".search-parent-" + searchType).each(function() {
      var txtValue = $(this).find(".filter-" + searchType).text().toUpperCase();
      if (txtValue.includes(filter)) {
        $(this).attr("style", "display:flex !important");
        noResultFound = false; // Update the existing variable
      } else {
        $(this).attr("style", "display:none !important");
      }
    });
    if (noResultFound) {
      var notFound = `
      <div class="custom_container">
        <div class="no_product_found_section">
          <h2>No result found</h2>
        </div>
      </div>`;
      $(".not-available-" + searchType).html(notFound);
    } else {
      $(".not-available-" + searchType).html(' ');
    }
  });

  $(document).ready(function() {

    const currentUrl = window.location.href;
    const urlObj = new URL(currentUrl);
    const pathname = urlObj.pathname;
    const pathSegments = pathname.split('/');
    let endpoint = pathSegments[pathSegments.length - 1];
    endpoint = endpoint.split('-').map(segment => segment.charAt(0).toLowerCase() + segment.slice(1))
    .join('-');
    function formatCategoryName(name) {
      const delimiter = "-";
      // Convert to lowercase
      let slug = name.toLowerCase();
      // Replace & with the delimiter
      slug = slug.replace(/&/g, delimiter);
      // Remove apostrophes
      slug = slug.replace(/'/g, '');
      // Replace all non-alphanumeric characters (excluding the delimiter) with the delimiter
      slug = slug.replace(/[^a-z0-9]+/g, delimiter);
      // Trim any delimiter characters from the beginning and end
      slug = slug.replace(new RegExp('^' + delimiter + '+|' + delimiter + '+$', 'g'), '');
      return slug;
    }
    var categories = $('.filter-items');
    categories.each(function() {
      const categoryName = $(this).text().trim();
      var id = $(this).data('id');
      var categorySlug = $(this).data('slug'); // Get the actual slug from data attribute
      const formattedCategoryName = formatCategoryName(categoryName);

      if (endpoint) {
        $('.reset_btn').attr('style', 'display:flex !important');
      }
      // Check against both formatted category name and actual slug
      if (formattedCategoryName === endpoint || categorySlug === endpoint) {
        $('#input'+ id).prop('checked', true);
        var filter_item = $('#' + id + ' .filter-items').text()
        var filters_html = `<span>${filter_item} <i class="fas fa-times remove_filter" data-id="${id}"></i></span>`;
        $('.show_selected_filters').append(filters_html);
      }
    });
    $('.reset_btn').on('click', function() {
      window.location.href = "/shop";
    })
    $('.remove_filter').on('click', function() {
      var id = $(this).data('id').toString();
      var currentUrl = new URL(window.location.href);
      var searchParams = new URLSearchParams(decodeURIComponent(currentUrl.search));

      if ($('.remove_filter').length === 1) {
        window.location.href = "/shop";
        return;
      }
      if (searchParams.has('filter')) {
        var searchValues = searchParams.get('filter').split(',');
        var newSearchValues = searchValues.filter(value => value !== id);
        if (newSearchValues.length > 0) {
          searchParams.set('filter', newSearchValues.join(','));
        } else {
          searchParams.delete('filter');
        }
      }
      var queryString = Array.from(searchParams.entries()).map(pair => `${pair[0]}=${pair[1]}`).join('&');
      var newUrl = "/search" + (queryString ? '?' + queryString : '');
      window.location.href = newUrl;
    });
    $(document).on('click', '#price_filter', function() {
      var startPrice = $('#minPrice').val();
      var endPrice = $('#maxPrice').val();
      if (startPrice && endPrice) {
        window.location.href = "/search" + "?price_range=" + startPrice + "to" + endPrice;
      }
    })
    function urlFormation() {
      const currentUrl = window.location.href;
      const urlObj = new URL(currentUrl);
      const searchParms = urlObj.search;
      return searchParms;
    }
    $('input[name="cat"]').change(function() {
      var checkedCheckboxes = $('input[name="cat"]:checked');
      var categoryIds = checkedCheckboxes.map(function() {
        return $(this).data('id');
      }).get().join(',');
      var parms = urlFormation();
      if (categoryIds != "" && categoryIds.length > 0) {
        var catqueryString = categoryIds ? '?filter=' + categoryIds : '';
        if (parms && parms.includes('query')) {
        var queryString = parms.replace('?','&');
          window.location.href = "/search" + catqueryString + queryString;
        }else{
          window.location.href = "/search" + catqueryString;
        }
      } else {
        window.location.href = "/shop";
      }
    });
    $('input[name="brand"]').change(function() {
      $('input[name="brand"]').not(this).prop('checked', false);
      var checkedCheckboxes = $('input[name="brand"]:checked');
      var brandids = checkedCheckboxes.map(function() {
        return $(this).data('id');
      }).get().join(',');
      var parms = urlFormation();
      if (brandids.length > 0) {
         var brandqueryString = brandids ? '?brand_id=' + brandids : '';
        if (parms && parms.includes('query')) {
          var queryString = parms.replace('?','&');
          window.location.href = "/search" + brandqueryString + queryString;
        }else{
          window.location.href = "/search" + brandqueryString;
        }
      } else {
        window.location.href = "/shop";
      }
    });
  });
</script>