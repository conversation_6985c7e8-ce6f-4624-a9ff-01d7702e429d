<?php

$headerPath = (isset($businessDetail['web_theme']) && $businessDetail['web_theme'] != "") ? '/../theme/partials/customHeader.phtml' : '/partials/header.phtml';
include __DIR__ . $headerPath;

$logo = isset($businessDetail['logo_image']) ? $businessDetail['logo_image'] : 'no_image.jpg';

?>


<style>
   .new_detail_page_grid_section_parent {
    display: flex;
    margin: 0px -15px;
}
    .product_detail_page_parent {
    padding: 30px 15px;
}

    .new_detail_page_grid_section_parent .col-lg-6.col-md-6.col-md-6.col-xs-12 {
        padding: 15px;
    }

    .item-unavailable-message {
        color: #F80000;
        font-weight: 600 !important;
        font-size: 18px;
    }

    ul.breadcrumbs {
        list-style: none;
        background-color: transparent;
        text-align: left ;
        margin: 0px;
    }

    ul.breadcrumbs li {
        display: inline;
        color: #808286;
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 26px;
    }

    ul.breadcrumbs li+li:before {
        padding: 8px;
        color: #121212;
        content: "/\00a0";
    }
    .loader_popup {
    margin-right: unset;
}
</style>
    
<script>
  $(document).ready(function () {
    let productdetails = <?= json_encode($data)?>;
    console.log(productdetails);
    viewEventDataLayer(productdetails);

    function viewEventDataLayer(productdetails){
      window.dataLayer = window.dataLayer || [];
      window.dataLayer.push({ ecommerce: null });  // Clear the previous ecommerce object
      window.dataLayer.push({
        event: "view_item",  // Changed from "viewItem" to "view_item" for GA4
        ecommerce: {
          currency: productdetails.currency,
          value: parseFloat(productdetails.price),
          items: [
            {
              item_id: productdetails.menu_cat_id,          
              item_name: productdetails.name,   
              item_category: productdetails.category,   
              price: parseFloat(productdetails.price),
              currency: productdetails.currency,
              quantity: 1
            }
          ]
        }
      });
    }
  })

</script>

<div class="product_detail_page_parent d-inline-block w-100">

    <div style="padding:0px" class="container detail-page-container">
    <?php echo $breadcrumbs?>
        <div class="new_detail_page_grid_section_parent row">
     
            <div class="col-lg-6 col-md-6 col-md-6 col-xs-12 padding_bottom_30 image_side">
            <div class="slider slider-for mb-4">
        
                </div>
                <?php
                echo count($data['images']);
                die();
                
                if (isset($data['images']) && count($data['images']) > 1) { ?>
                <div class="multiple_images_box slider slider-nav">
                  <?php foreach ($data['images'] as $index => $image): ?>
                    <div>
                      <img style="cursor:pointer" data-slide="<?= $index ?>" src="<?= $image['image'] ?>" width="150px"
                        class="object-fit-cover clickable-thumbnail" alt="<?= $title ?>" />
                    </div>
                  <?php endforeach; ?>
                </div>
                <?php } ?>

                  <?php foreach ($data['images'] as $index => $image): ?>
                    <div>
                      <img width="100%" height="600px" src="<?= $data['images'] ?>" class="object-fit-cover" alt="<?= $title ?>"
                        style="margin-right: 10px;" />
                    </div>
                  <?php endforeach; ?>
            </div>
            <div class="col-lg-6 col-md-6 col-md-6 col-xs-12 padding_bottom_30 product_detail_side">

                <?php echo ($detailPageContent['detail_page_html'] == "" || $detailPageContent['detail_page_html'] == null) ? '' : eval("?>" . html_entity_decode($detailPageContent['detail_page_html']) . "<?php ");
                $jsonData = json_encode($data);
                $encodedData = base64_encode($jsonData);
                echo "<input type='hidden' id='" . $data['menu_item_id'] . "_product' class='hidden_field product_detail' value='" . htmlspecialchars($encodedData, ENT_QUOTES, 'UTF-8') . "'>";
                ?>
            </div>
        </div>
    </div>
    <?php
    if (isset($data['suggestions']) && !empty($data['suggestions'])) {
        include __DIR__ . '/partials/suggesstions.phtml';
    }
    ?>

</div>

<?php



$headerPath = (isset($web_theme) && !empty($web_theme)) ? '/../theme/partials/customfooter.phtml' : '/partials/footer.phtml';
include __DIR__ . $headerPath;
include __DIR__ . '/partials/cartSummary.phtml';
