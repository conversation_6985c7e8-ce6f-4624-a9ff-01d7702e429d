<!DOCTYPE html>
<html>

<head>
    <!-- Meta tags for page information and compatibility -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"/>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="description" content="<?= isset($desc) && $desc != '' ? $desc : $businessDetail["name"] ?>">
    <?php 
        $domainFlag = isset($businessDetail) && isset($businessDetail['domain']) && $businessDetail['domain'] != "";
        $currentDomain = $_SERVER['HTTP_HOST'];
        if (!$domainFlag || strpos($currentDomain, 'ordrz.com') === false) {
            echo '<meta name="robots" content="index, follow">';
        } else {
            echo '<meta name="robots" content="noindex, nofollow">';
        }
    ?>

    <!-- Page Title -->
    <title><?= isset($title) && $title != '' ? $title : $businessDetail["name"] ?></title>

    <!-- Favicon -->
    <link rel="shortcut icon" type="image/x-icon" href="<?php echo $this->url->get('img/favicon.ico') ?>" />

    <!-- Open Graph tags for social media sharing -->
    <meta property="og:title" content="<?= isset($title) && $title != '' ? $title : $businessDetail["name"] ?>">
    <meta property="og:description" content="<?= isset($desc) && $desc != '' ? $desc : $businessDetail["name"] ?>">
    <meta property="og:image" content="<?= isset($businessDetail['logo_image']) && $businessDetail['logo_image'] != '' ? $businessDetail['logo_image'] : '' ?>">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="<?= isset($businessDetail['name']) && $businessDetail['name'] != '' ? $businessDetail['name'] : '' ?>">
      <!-- Keywords for SEO -->
    <meta name="keywords" content="<?= isset($keywords) && $keywords != '' ? $keywords : $businessDetail["name"] ?>">
    <?php 
    if(isset($staticPage) && $staticPage == 1){
       echo  "<meta name='page-type' content='static'>";
    } 

    if (!empty($businessDetail['tag_manager_head'])) {
        echo html_entity_decode($businessDetail['tag_manager_head']);
    }
    if (!empty($businessDetail['facebook_pixel'])) {
        
        echo html_entity_decode($businessDetail['facebook_pixel']);

    }
    if (!empty($businessDetail['google_analytics_code'])) {
        echo html_entity_decode($businessDetail['google_analytics_code']);
    }

    if (!empty($businessDetail['google_webmaster'])) {
        echo html_entity_decode($businessDetail['google_webmaster']);
    }
     ?>
  
    <!-- Bootstrap 5 CSS -->
    <link rel="stylesheet" href="http://localhost:8000/css/bootstrap.min.css" crossorigin="anonymous" />

    <!-- Slick carousel CSS -->
    <link media="print" onload="this.media='all'" rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/jquery.slick/1.4.1/slick.css" />

    <!-- Font Awesome icons -->
    <link media="print" onload="this.media='all'" href="https://use.fontawesome.com/releases/v5.0.6/css/all.css" rel="stylesheet"/>
    <link media="print" onload="this.media='all'" rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.10.0/css/all.css" integrity="sha384-AYmEC3Yw5cVb3ZcuHtOA93w35dYTsvhLPVnYs9eStHfGJvOvKxVfELGroGkvsg+p" crossorigin="anonymous"/>
    <link media="print" onload="this.media='all'" rel="stylesheet" href="https://use.fontawesome.com/releases/v5.3.1/css/all.css" integrity="sha384-mzrmE5qonljUremFsqc01SB46JvROS7bZs3IO2EmfFsd15uHvIt+Y8vEf7N7fWAU" crossorigin="anonymous"/>
    <link media="print" onload="this.media='all'" rel="stylesheet" href="https://pro.fontawesome.com/releases/v5.15.3/css/all.css" integrity="sha384-iKbFRxucmOHIcpWdX9NTZ5WETOPm0Goy0WmfyNcl52qSYtc2Buk0NCe6jU1sWWNB" crossorigin="anonymous">
    <!-- Site favicon -->
    <link rel="icon" type="image/x-icon" href="<?= $businessDetail['favicon'] ?>">

    <link media="print" onload="this.media='all'"  rel="preconnect" href="https://fonts.googleapis.com">
    <link media="print" onload="this.media='all'"  rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link media="print" onload="this.media='all'"  href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">

    <!-- Custom site CSS -->
    <link rel="stylesheet" href="http://localhost:8000/css/style.css" />

    <link media="print" onload="this.media='all'" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/css/intlTelInput.css"/>

    <!-- jQuery JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap 5 JS -->
    <script src="http://localhost:8000/js/bootstrap.bundle.min.js" defer></script> 

    <!-- Slick carousel JS -->
    <script type="text/javascript" src="//cdn.jsdelivr.net/jquery.slick/1.4.1/slick.min.js" defer></script>

    <!--  (Google Tag Manager, Analytics, etc.) -->
    <?php
    if (isset($theme_setting->theme_settings) && isset($theme_setting->theme_settings->app_page_on_off) && $theme_setting->theme_settings->app_page_on_off == 1) { ?>
 <!-- Start Custom SmartBanner configuration -->
    <meta name="smartbanner:title" content="<?php echo $businessDetail['name'];?>">
      <meta name="smartbanner:author" content="<?php echo $businessDetail['name'];?>">
      <meta name="smartbanner:images" content="<?php echo $businessDetail["logo_image"];?>">
      <meta name="smartbanner:price" content="FREE">
      <meta name="smartbanner:price-suffix-apple" content=" - On the App Store">
      <meta name="smartbanner:price-suffix-google" content=" - In Google Play">
      <meta name="smartbanner:icon-apple" content="<?php echo ASSETS_PATH; ?>/images/apple-app-store.png">
      <meta name="smartbanner:icon-google" content="<?php echo ASSETS_PATH; ?>/images/google-app-store.png">
      <meta name="smartbanner:button" content="Install App">
      <meta name="smartbanner:button-url-apple" content="<?php echo $theme_setting->theme_settings->iphone_app_url;?>">
      <meta name="smartbanner:button-url-google" content="<?php echo $theme_setting->theme_settings->android_app_url;?>">
      <meta name="smartbanner:enabled-platforms" content="android,ios">
      <!-- End Custom SmartBanner configuration -->
      
    <?php
        include __DIR__ . '/index/partials/smartBanner.phtml';    
}

?>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-PZPQMMTDF2"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'G-PZPQMMTDF2', {
    'cookie_domain': 'auto'
  });
  gtag('event', 'page_view');
</script>
</head>

<body data-country-code="<?= $_SERVER["HTTP_CF_IPCOUNTRY"] ?>">
    <!-- Dynamic heading based on content -->
    <h1 class="hide"> <?= isset($h1) && $h1 != null ? $h1 : '' ?></h1>

    <!-- Page content injected dynamically -->
    <?php echo $this->getContent(); ?>

    <!-- JavaScript variables for use in scripts -->
    <script>
        var business_id = <?php echo $businessDetail['res_id']; ?>;
        var brand_id = <?php echo $businessDetail['brand_id']; ?>;
        var baseURL = "<?php echo 'https://' . $_SERVER['SERVER_NAME']; ?>";
        var api_path = "<?php echo API_PATH ?>";
        var webSearchUrl = "/api/searchSuggestion";
        var carBtnStatus = "<?php echo isset($cart_btn_status) && $cart_btn_status != '' ? $cart_btn_status : '' ; ?>";

        if (carBtnStatus && carBtnStatus == "show") {
            $("header.web_header").addClass("sticky-header");
            $("header.mobile-header").addClass("sticky-header");
        }
    </script>

    <!-- Additional custom JS -->
    <script src="http://localhost:8000/js/home.js" defer></script>
    <script src="http://localhost:8000/js/cart.js" defer></script>
    <script src="http://localhost:8000/js/validate.js" defer> </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js" defer></script>

</body>

</html>