

<?php
    $image_parts = explode('/', $item['image']);  // Assign the result to a variable
    $product_name = end($image_parts);
    $cards_to_show =  isset($cardsToShow) ? $cardsToShow: '' ;
    $col_class = 'col-lg-' . ($cards_to_show);
    $currency_symbol = "";
    if (isset($item['currency'])) {
        $currency_symbol = getCurrencySymbol($item['currency']);
    }else{
        $currency_symbol = getCurrencySymbol($currency);
    }
    ?>
<div class="my-3 single_product_theme product-card-<?= $item['menu_item_id']?> col-6 col-md-6 <?= $col_class?> <?= $item['status'] == 1 ? "out_of_stock_cart" : ""; ?>">
        <div style='
            background: <?= $searchResult['settings']['card_bg_color'] ?>;
            border: <?= $searchResult['settings']['card_border'] ?>;
            box-shadow: <?= $searchResult['settings']['card_shadow'] ?>;
        ' class="product_card_theme w-100">
            <div class="product_content_theme d-flex flex-column w-100">
       
                <?php
      $isParent = false;
      $totalItems = count($searchResult["items"]);
      $currentIndex = 0;
      $counterStyles = '';

      foreach ($searchResult["items"] as $settings) {
        $currentIndex++;

        $type = $settings['type'];
        $config = $settings['settings'];
        $item_name = strtolower($item['name']);
        $delimiter = "-";
        $slug = strtolower(trim(preg_replace('/[\s-]+/', $delimiter, preg_replace('/[^A-Za-z0-9-]+/', $delimiter, preg_replace('/[&]/', '', preg_replace('/[\']/', '', iconv('UTF-8', 'ASCII//TRANSLIT', $item_name))))), $delimiter));
        // Render image
        if ($type == 'image' && $config['image_on_off'] == 1) {
            echo "<a class='control_pointer_event' href='/product/" . $slug . '-' . $item['menu_item_id'] . "'><figure class='w-100 h-100 " . ($item['image'] == 'no_image.jpg' ? 'd-none' : '') . "'>";
            if ($item['image'] != 'no_image.jpg') {
                echo "<img
                style='
                    object-fit: " . $config['image_property'] . ";
                    height: " . $config['image_height'] . ";
                    width: " . $config['image_width'] . ";
                    background-color: " . $config['image_bg_color'] . ";
                    border-radius: " . $config['image_border_radius'] . ";
                    margin: " . $config['image_margin'] . ";
                    padding: " . $config['image_padding'] . ";
                    font-size: " . $config['image_font_size'] . ";
                    border-bottom: " . $config['image_border_bottom'] . ";
                    border: " . $config['image_border'] . ";
                    font-weight: " . $config['image_font_weight'] . ";
                    float: " . $config['image_float'] . ";
                    text-align: " . $config['image_text_align'] . ";
                    position: " . $config['image_position'] . ";
                    top: " . $config['image_top'] . ";
                    right: " . $config['image_right'] . ";
                    left: " . $config['image_left'] . ";
                '
                loading='lazy' src='" . (basename($item['image']) == 'no_image.jpg' ? (isset($logo) && $logo != "" ? $logo : $item['image']) : $item['image']) . "' class='w-100 ' alt='".$item['name']."'>";
            }
            echo "</figure></a>";
        }
        if(!$isParent){
            $isParent = true;
            echo "<div class='card-product-info'>";
         
        }
        // Render category
        if ($type == 'category' && $config['category_on_off'] == 1) {
            echo "<p style='
                float: " . $config['category_float'] . ";
                text-align: " . $config['category_text_align'] . ";
                color: " . $config['category_color'] . ";
                background-color: " . $config['category_bg_color'] . ";
                border-radius: " . $config['category_border_radius'] . ";
                margin: " . $config['category_margin'] . ";
                padding: " . $config['category_padding'] . ";
                font-size: " . $config['category_font_size'] . ";
                border: " . $config['category_border'] . ";
                border-top: " . $config['category_border_top'] . ";
                border-bottom: " . $config['category_border_bottom'] . ";
                font-weight: " . $config['category_font_weight'] . ";
                width: " . $config['category_width'] . ";
                height: " . $config['category_height'] . ";
                position: " . $config['category_position'] . ";
                top: " . $config['category_top'] . ";
                right: " . $config['category_right'] . ";
                left: " . $config['category_left'] . ";
            ' class='theme-category mb-0'>" . $item['category'] . "</p>";
        }
    
        // Render name
        if ($type == 'name' && $config['name_on_off'] == 1) {
            $image_name = explode('/', $item['image']);
            $product_name = end($image_name);
            $item_name = strtolower($item['name']);
            $delimiter = "-";
            $slug = strtolower(trim(preg_replace('/[\s-]+/', $delimiter, preg_replace('/[^A-Za-z0-9-]+/', $delimiter, preg_replace('/[&]/', '', preg_replace('/[\']/', '', iconv('UTF-8', 'ASCII//TRANSLIT', $item_name))))), $delimiter));
            echo "<a class='control_pointer_event' href=/product/" . $slug . '-' . $item['menu_item_id']. ">
                <p style='
                    color: " . $config['name_color'] . ";
                    text-align: " . $config['name_text_align'] . ";
                    font-size: " . $config['name_font_size'] . ";
                    font-weight: " . $config['name_font_weight'] . ";
                    margin: " . $config['name_margin'] . ";
                    background-color: " . $config['name_bg_color'] . ";
                    border-radius: " . $config['name_border_radius'] . ";
                    width: " . $config['name_width'] . ";
                    padding: " . $config['name_padding'] . ";
                    border: " . $config['name_border'] . ";
                    border-top: " . $config['name_border_top'] . ";
                    border-bottom: " . $config['name_border_bottom'] . ";
                    float: " . $config['name_float'] . ";
                    height: " . $config['name_height'] . ";
                    position: " . $config['name_position'] . ";
                    top: " . $config['name_top'] . ";
                    right: " . $config['name_right'] . ";
                    left: " . $config['name_left'] . ";
                ' class='product_name_theme'>" . $item['name'];
    
            if (trim($item['item_weight']) != "") {
                echo "<span style='opacity: .7; font-size: 12px; color: #222222; font-weight: 400;margin-left: 5px;' class='card-weight'>(" . $item['item_weight'] . ")</span>";
            }
    
            echo "</p></a>";
        }
    
        // Render discount percentage
        if ($type == 'discount_percentage' && $config['discount_percentage_on_off'] == 1 && isset($item['discount']) && $item['discount'] > 0) {
            echo "<p style='
                color: " . $config['discount_percentage_color'] . ";
                background-color: " . $config['discount_percentage_bg_color'] . ";
                font-size: " . $config['discount_percentage_font_size'] . ";
                font-weight: " . $config['discount_percentage_font_weight'] . ";
                padding: " . $config['discount_percentage_padding'] . ";
                position: " . $config['discount_percentage_position'] . ";
                top: " . $config['discount_percentage_top'] . "px;
                left: " . $config['discount_percentage_left'] . "px;
                margin: " . $config['discount_percentage_margin'] . ";
                border-radius: " . $config['discount_percentage_border_radius'] . ";
                border: " . $config['discount_percentage_border'] . ";
                width: " . $config['discount_percentage_width'] . ";
                height: " . $config['discount_percentage_height'] . ";
                text-align: " . $config['discount_percentage_text_align'] . ";
                border-bottom: " . $config['discount_percentage_border_bottom'] . ";
            ' class='card-discount_percentage_theme mb-0'> <span>" . number_format($item['discount']) . " %</span> OFF</p>";
        }
    
        // Render description
        if ($type == 'description' && isset($item['desc']) && $config['description_on_off'] == 1) {
            echo "<div style='
                color: " . $config['description_color'] . ";
                text-align: " . $config['description_text_align'] . ";
                font-size: " . $config['description_font_size'] . ";
                margin: " . $config['description_margin'] . ";
                padding: " . $config['description_padding'] . ";
                border: " . $config['description_border'] . ";
                border-top: " . $config['description_border_top'] . ";
                border-bottom: " . $config['description_border_bottom'] . ";
                line-height: " . $config['description_line_height'] . ";
                font-weight: " . $config['description_font_weight'] . ";
                background-color: " . $config['description_bg_color'] . ";
                width: " . $config['description_width'] . ";
                height: " . $config['description_height'] . ";
                border-radius: " . $config['description_border_radius'] . ";
                float: " . $config['description_float'] . ";
                position: " . $config['description_position'] . ";
                top: " . $config['description_top'] . ";
                right: " . $config['description_right'] . ";
                left: " . $config['description_left'] . ";
            ' class='product_description_theme'>" . stripHtmlTags($item['desc']) . "</div>";
        }
        
        // Render add to cart button
        if ($type == 'add_to_cart' && $config['add_to_cart_on_off'] == 1 && $item['status'] != 1 && $cart_feature_exsits == "cart" && $cart_btn_status == "show") {
            $url = '';
            $class = '';
            if(isset($searchResult['settings']['product_code']) && $searchResult['settings']['product_code'] == 1){
                $url = $searchResult['settings']['domain'] . "/product/" . $slug . '-' . $item['product_code'];
                $class='';
            }else{
                $url = 'javascript:void(0);';
                $class='pop_up_modal';
            }
            $styles = "background-color: {$config['add_to_cart_bg_color']};";
            $styles .= "width: {$config['add_to_cart_width']};";
            $styles .= "border-radius: {$config['add_to_cart_border_radius']};";
            $styles .= "margin: {$config['add_to_cart_margin']};";
            $styles .= "padding: {$config['add_to_cart_padding']};";
            $styles .= "font-size: {$config['add_to_cart_font_size']};";
            $styles .= "border: {$config['add_to_cart_border']};";
            $styles .= "border-top: {$config['add_to_cart_border_top']};";
            $styles .= "border-bottom: {$config['add_to_cart_border_bottom']};";
            $styles .= "font-weight: {$config['add_to_cart_font_weight']};";
            $styles .= "float: {$config['add_to_cart_float']};";
            $styles .= "text-align: {$config['add_to_cart_text_align']};";
            $styles .= "height: {$config['add_to_cart_height']};";
            $styles .= "position: {$config['add_to_cart_position']};";
            $styles .= "top: {$config['add_to_cart_top']};";
            $styles .= "right: {$config['add_to_cart_right']};";
            $styles .= "left: {$config['add_to_cart_left']};";
            $styles .= "color: {$config['add_to_cart_color']};";
    
            echo "<div class='product_cart_btn'
            style='
            display:flex;
            justify-content: " .$config['add_to_cart_text_align']. "'
            ' 
            >
                <a style='{$styles}' id='" . $item['menu_item_id'] . "' class='".   $class  ."' href='". $url  ."'>". $config['add_to_cart_label']."</a>
            </div>";
        }

        if ($type == 'counter' && $config['counter_on_off'] == 1 && $item['status'] != 1 && $cart_feature_exsits == "cart" && $cart_btn_status == "show") {
            $counterStyles .= "width: {$config['counter_width']};";
            $counterStyles .= "border-radius: {$config['counter_border_radius']};";
            $counterStyles .= "margin: {$config['counter_margin']};";
            $counterStyles .= "padding: {$config['counter_padding']};";
            $counterStyles .= "font-size: {$config['counter_font_size']};";
            $counterStyles .= "border: {$config['counter_border']};";
            $counterStyles .= "font-weight: {$config['counter_font_weight']};";
            $counterStyles .= "float: {$config['counter_float']};";
            $counterStyles .= "text-align: {$config['counter_text_align']};";
            $counterStyles .= "height: {$config['counter_height']};";
            $counterStyles .= "position: {$config['counter_position']};";
            $counterStyles .= "top: {$config['counter_top']};";
            $counterStyles .= "right: {$config['counter_right']};";
            $counterStyles .= "left: {$config['counter_left']};";
            $counterStyles .= "color: {$config['counter_color']};";
    
            echo "<div class='product_cart_btn' style='display:flex;'>";

            // Decrease Quantity Button
            echo "<a 
                data-fb-track='true'
                role='button'
                aria-label='Decrease quantity'
                id='decrease_" . $item['menu_item_id'] . "' 
                class='cart_button_reduce_" . $item['menu_item_id'] . " reduce_qty hide decrease-qty-btn'
                data-tracking-type='decrease'
                href='#'
                style='{$counterStyles}'>
                    <i class='fas fa-minus'></i>
            </a>";
        
            // Quantity Span
            echo "<span 
                id='" . $item['menu_item_id'] . "_item_qty' 
                class='total_qty_product hide'>0
            </span>";
        
            // Increase/Add to Cart Button
            $addToCartClass = isset($item['options']) && empty($item['options']) 
                ? "add_to_cart cart_button_" . $item['menu_item_id'] . " add-to-cart-btn" 
                : "pop_up_modal add-to-cart-btn";
        
            echo "<a 
                data-fb-track='true'
                role='button'
                aria-label='Add to cart'
                id='increase_" . $item['menu_item_id'] . "' 
                class='" . $addToCartClass . "'
                href='#'
                data-tracking-type='increase'
                style='{$counterStyles}'>
                    <i class='fas fa-plus'></i>
            </a>";
        
        echo "</div>";
        
        }
    
        // Render not available indicator
        if ($type == 'not_available' && $config['not_available_on_off'] == 1 && $item['status'] == 1) {
            echo "<p class='out_of_stock_theme' style='
                color: " . $config['not_available_color'] . ";
                font-size: " . $config['not_available_font_size'] . ";
                font-weight: " . $config['not_available_font_weight'] . ";
                text-align: " . $config['not_available_text_align'] . ";
                padding: " . $config['not_available_padding'] . ";
            '>
            " . (isset($theme_setting->theme_settings->product_availability_text) && $theme_setting->theme_settings->product_availability_text != "" ? $theme_setting->theme_settings->product_availability_text : "Out of Stock") . "
            </p>";
        }
    
        // Render attributes
        $attributes = [];
        if (isset($item['attributes']) && !empty($item['attributes'])) {
            $attributes = call_user_func_array('array_merge', $item['attributes']);
        }
        if ($type == 'attributes' && isset($config['attributes_on_off']) && $config['attributes_on_off'] == 1) {
            foreach ($attributes as $key => $attribute) {
                echo "<p class='attribute-card-theme' style='
                color: " . $config['attributes_color'] . ";
                background-color: " . $config['attributes_bg_color'] . ";
                border-radius: " . $config['attributes_border_radius'] . ";
                margin: " . $config['attributes_margin'] . ";
                padding: " . $config['attributes_padding'] . ";
                font-size: " . $config['attributes_font_size'] . ";
                border: " . $config['attributes_border'] . ";
                border-top: " . $config['attributes_border_top'] . ";
                border-bottom: " . $config['attributes_border_bottom'] . ";
                font-weight: " . $config['attributes_font_weight'] . ";
                float: " . $config['attributes_float'] . ";
                text-align: " . $config['attributes_text_align'] . ";
                position: " . $config['attributes_position'] . ";
                top: " . $config['attributes_top'] . ";
                right: " . $config['attributes_right'] . ";
                left: " . $config['attributes_left'] . ";
                height: " . $config['attributes_height'] . ";
                width: " . $config['attributes_width'] . ";
                '>
                " . $attribute."
                </p>";
            }
            }
    
        // Render wishlist
        if ($type == 'wishlist' && $config['wishlist_on_off'] == 1) {
            echo "<div class='wishlist-icon' style='
                color: " . $config['wishlist_color'] . ";
                background-color: " . $config['wishlist_bg_color'] . ";
                border-radius: " . $config['wishlist_border_radius'] . ";
                margin: " . $config['wishlist_margin'] . ";
                padding: " . $config['wishlist_padding'] . ";
                font-size: " . $config['wishlist_font_size'] . ";
                border: " . $config['wishlist_border'] . ";
                border-top: " . $config['wishlist_border_top'] . ";
                border-bottom: " . $config['wishlist_border_bottom'] . ";
                font-weight: " . $config['wishlist_font_weight'] . ";
                float: " . $config['wishlist_float'] . ";
                text-align: " . $config['wishlist_text_align'] . ";
                position: " . $config['wishlist_position'] . ";
                top: " . $config['wishlist_top'] . ";
                right: " . $config['wishlist_right'] . ";
                left: " . $config['wishlist_left'] . ";
                height: " . $config['wishlist_height'] . ";
                width: " . $config['wishlist_width'] . ";
            '>
                <i class='" . $config['wishlist_icon'] . "'></i>
            </div>";
        }
    
        // Render SKU
        if ($type == 'sku' && $config['sku_on_off'] == 1) {
            echo "<p class='sku-card-theme' style='
                color: " . $config['sku_color'] . ";
                background-color: " . $config['sku_bg_color'] . ";
                border-radius: " . $config['sku_border_radius'] . ";
                margin: " . $config['sku_margin'] . ";
                padding: " . $config['sku_padding'] . ";
                font-size: " . $config['sku_font_size'] . ";
                border: " . $config['sku_border'] . ";
                font-weight: " . $config['sku_font_weight'] . ";
                float: " . $config['sku_float'] . ";
                text-align: " . $config['sku_text_align'] . ";
            '>
            " . $item['sku'] . "
            </p>";
        }

        // Render brand
        if ($type == 'brand' && $config['brand_on_off'] == 1) {
            echo "<p class='brand-card-theme' style='
                color: " . $config['brand_color'] . ";
                background-color: " . $config['brand_bg_color'] . ";
                border-radius: " . $config['brand_border_radius'] . ";
                margin: " . $config['brand_margin'] . ";
                padding: " . $config['brand_padding'] . ";
                font-size: " . $config['brand_font_size'] . ";
                border: " . $config['brand_border'] . ";
                border-top: " . $config['brand_border_top'] . ";
                border-bottom: " . $config['brand_border_bottom'] . ";
                font-weight: " . $config['brand_font_weight'] . ";
                float: " . $config['brand_float'] . ";
                text-align: " . $config['brand_text_align'] . ";
            '>
            " . isset($item['brand']) && !empty($item['brand']) ? $item['brand'] : '' . "
            </p>";
        }
                       // Render discounted price
        if ($type == 'discounted_price' && $config['discounted_price_on_off'] == 1 && ($item['discount_display'] != 0 || $item['discount_display'] != 0.00)) {
                        $discountedPrice = $item['price'] - $item['discount_display'];
                       
                        echo "<p class='text_decoration_line_through' style='
                                color: " . $config['discounted_price_color'] . ";
                                background-color: " . $config['discounted_price_bg_color'] . ";
                                border-radius: " . $config['discounted_price_border_radius'] . ";
                                margin: " . $config['discounted_price_margin'] . ";
                                padding: " . $config['discounted_price_padding'] . ";
                                font-size: " . $config['discounted_price_font_size'] . ";
                                border: " . $config['discounted_price_border'] . ";
                                border-top: " . $config['discounted_price_border_top'] . ";
                                border-bottom: " . $config['discounted_price_border_bottom'] . ";
                                font-weight: " . $config['discounted_price_font_weight'] . ";
                                float: " . $config['discounted_price_float'] . ";
                                text-align: " . $config['discounted_price_text_align'] . ";
                                position: " . $config['discounted_price_position'] . ";
                                top: " . $config['discounted_price_top'] . ";
                                right: " . $config['discounted_price_right'] . ";
                                left: " . $config['discounted_price_left'] . ";
                                height: " . $config['discounted_price_height'] . ";
                                width: " . $config['discounted_price_width'] . ";
                            '>
                                        " .$currency_symbol ." ".  formatPrice($item['price'],$decimal_places) . "
                            </p>";
        }
  // Render price
        if ($type == 'price' && $config['price_on_off'] == 1) {
            $actualPrice = isset($item['price']) && $item['price'] != 0 ? $item['price']  : (isset($item['options'][0]['items'][0]['price']) ? $item['options'][0]['items'][0]['price'] : 0);
            $pricePer = !empty($item['price_per']) ? "<small class='price_per_theme'>{$item['price_per']}</small>" : '';
            if (isset($item['discount_display']) && $item['discount_display'] != 0) {

                $actualPrice -= $item['discount_display'];
            }

            echo "<p class='orginal_price " . (isset($item['discount_display']) && $item['discount_display'] == 0 ? 'discounted-price-line-through' : '') . "';
                style='
                        display:block;
                        width:100%;
                        color: " . $config['price_color'] . ";
                        font-size: " . $config['price_font_size'] . ";
                        font-weight: " . $config['price_font_weight'] . ";
                        background-color: " . $config['price_bg_color'] . ";
                        border-radius: " . $config['price_border_radius'] . ";
                        margin: " . $config['price_margin'] . ";
                        padding: " . $config['price_padding'] . ";
                        border: " . $config['price_border'] . ";
                        border-top: " . $config['price_border_top'] . ";
                        border-bottom: " . $config['price_border_bottom'] . ";
                        height: " . $config['price_height'] . ";
                        position: " . $config['price_position'] . ";
                        top: " . $config['price_top'] . ";
                        right: " . $config['price_right'] . ";
                        left: " . $config['price_left'] . ";
                '>
                        " . (
                            (isset($currency_symbol) && isset($item['price']) && $item['price'] != 0)
                                ? '<span class="item-currency" id="currency">' . $currency_symbol . '</span>'
                                : '<span class="price_range">From </span><span class="item-currency" id="currency">' . $currency_symbol . '</span>'
                        ) . " <span class='price-value'> ".formatPrice($actualPrice,$decimal_places) . $pricePer ." </span>  </p>";
}     
        if ($currentIndex == $totalItems) {
            $jsonData = json_encode($item);
            $encodedData = base64_encode($jsonData);
            echo "<input type='hidden' id='" . $item['menu_item_id'] . "_product' class='hidden_field' value='" . htmlspecialchars($encodedData, ENT_QUOTES, 'UTF-8') . "'>";
            echo "<input type='hidden' class='currency' value='" . $currency_symbol . "'>";
            echo "</div>";
        }
    }
    
                ?>         
               
            </div>
        </div>
    </div>
