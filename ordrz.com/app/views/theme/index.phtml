<style>
    .row.category-section {
        width: 100%;
        justify-content: center;
        margin-bottom: 50px;
        flex-wrap: wrap;
    }
 
    .row.category-section img {
        width: 100%;
    }
    .category-card {
        flex: 0 0 25%;
    }
    .category-card h5 {
        text-align: center;
    }
    .flex-container {
        display: flex;
        justify-content: flex-start;
    }
    .flex-left {
        justify-content: flex-start;
    }
    .flex-center {
        justify-content: center;
    }
    .flex-right {
        justify-content: flex-end;
    }
    .flex-item {
        margin: 0 10px;
    }
    a{
        text-decoration: none;
        color:#212529; 
    }
</style>

<?php
$theme = json_decode($businessDetail['web_theme'], true);
$cart_feature_exsits = $businessDetail['feature'];
$cart_btn_status = $businessDetail['status'];
$logo = isset($businessDetail['logo_image']) ? $businessDetail['logo_image'] : 'no_image.jpg';
?>
<style>
</style>
<?php include __DIR__ . '/partials/customHeader.phtml'; ?>
<?php include __DIR__ . '/partials/cartSummary.phtml'; ?>
<?php include __DIR__ . '/partials/slider.phtml'; ?>
<?php include __DIR__ . '/../index/partials/popup.phtml'; ?>

<?php
$section = $theme;
$theme_setting = json_decode($businessDetail['theme_settings']);
if (isset($section['home']['sections']) && is_array($section['home']['sections'])) {
    foreach ($section['home']['sections'] as $keyType => $data) {
        $searchResult = $section['card']['search_results'];
        if (isset($data['status'], $data['type'], $data['platform']) && $data['status'] == "1" && ($data['type'] == "custom" || $data['type'] == "featured" || $data['type'] == "category") && ($data["platform"] == 'web' || $data["platform"] == 'all')) {
            if ($data['type'] == "custom" && isset($data['design'])) {
                render_custom_design($data['design'] , $theme_setting);
            } elseif ($data['type'] == "featured" && isset($data['featured_products']) && is_array($data['featured_products'])) {
                $category = [];
                $branch_id = isset($_COOKIE['branch_id']) ? $_COOKIE['branch_id'] : '';
                // Process featured products and filter by branch availability
                foreach ($data['featured_products'] as $row) {
                    $is_available_in_branch = true;
                    
                    // Check if product is not available in current branch
                    if (!empty($branch_id) && isset($row['branch_na']) && is_array($row['branch_na'])) {
                        if (in_array($branch_id, $row['branch_na'])) {
                            $is_available_in_branch = false;
                        }
                    }
                    
                    // Only add category if product is available in the selected branch
                    if ($is_available_in_branch) {
                        if (!in_array($row['category'], $category)) {
                            array_push($category, $row['category']);
                        }
                    }
                }
                if ($data['tabs'] == 1) {
                    render_featured_products($data, $keyType, $category, $searchResult  , $cart_btn_status , $cart_feature_exsits , $logo  , $branch_id, $decimal_places);
                } else {
                    render_featured_products_with_tabs($data, $keyType, $category, $searchResult , $cart_btn_status , $cart_feature_exsits, $logo  , $branch_id, $decimal_places);
                }
            } elseif ($data['type'] == "category" && isset($data['featured_category']['result']) && is_array($data['featured_category']['result'])) {
                render_category_section($data);
            }
        }
    }
}
if (isset($theme['footer'])) {
        include __DIR__ . '/partials/customfooter.phtml';
}

function render_custom_design($design , $theme_setting) {
    $recaptcha_site_key = null;
    if (isset($theme_setting) && isset($theme_setting->theme_settings) && isset($theme_setting->theme_settings->recaptcha_site_key)) {
        $recaptcha_site_key  = $theme_setting->theme_settings->recaptcha_site_key;
    }
    eval('?>' . $design);
}

function render_featured_product($item, $searchResult, $cardsToShow , $cart_btn_status , $cart_feature_exsits , $logo,$decimal_places) {
    include __DIR__ . '/partials/customCards.phtml'; 
}

function render_featured_products($data, $keyType, $category, $searchResult  , $cart_btn_status , $cart_feature_exsits , $logo , $branch_id, $decimal_places) {
    $cardsToShow = $data['cards'];
    ?>
    <div class="feature_section main_feature_parent_class_<?= $keyType?>">
        <div class='custom_feature_section_figure'></div>
        <div class="container">
            <div class="feature_parent_<?= $keyType?>">
                <?php if(isset($data['name']) && $data['name'] != "") { ?>
                    <div class="feature_section_heading_<?= $keyType; ?>">
                        <?= $data['name']; ?>                        
                    </div>
                <?php } ?>
                <ul class="nav feature_section_nav_<?= $keyType?>" id="myTab" role="tablist">
                <?php if (in_array('all', $data['featured_items'])): ?>
                    <li class="nav-item">
                        <a class="nav-link active" id="all-tab" data-bs-toggle="tab" href="#all" role="tab" aria-controls="all" aria-selected="true">All</a>
                    </li>
                <?php endif; ?>
                <?php 
                $j = 1;
                foreach ($category as $c):
                    $val = strtolower(preg_replace('#[^a-z0-9_]#i', '', $c));
                    $activeClass = ($j == 1 && !in_array('all', $data['featured_items'])) ? 'active' : '';
                ?>
                    <li class="nav-item">
                        <a class="nav-link <?= $activeClass; ?>" id="tab-<?= $val; ?>" data-bs-toggle="tab" href="#featured_section_<?= $keyType; ?>_<?= $val; ?>" role="tab" aria-controls="featured_section_<?= $keyType; ?>_<?= $val; ?>" aria-selected="false"><?= $c; ?></a>
                    </li>
                <?php $j++; endforeach; ?>
                </ul>
            </div>
            <div class="tab-content">
                <?php if (in_array('all', $data['featured_items'])): ?>
                    <div class="tab-pane fade show active" id="all" role="tabpanel" aria-labelledby="all-tab">
                        <div class="row">
                            <?php foreach ($data['featured_products'] as $item): 
                                     if ($branch_id == "" || (isset($item['branch_na']) && !in_array($branch_id , $item['branch_na']))){ ?>
                                <?= render_featured_product($item, $searchResult, $cardsToShow  , $cart_btn_status , $cart_feature_exsits , $logo,$decimal_places); ?>
                            <?php } endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                <?php 
                $jj = 1;
                foreach ($category as $c):
                    $val = strtolower(preg_replace('#[^a-z0-9_]#i', '', $c));
                    $activeClass = ($jj == 1 && !in_array('all', $data['featured_items'])) ? 'show active' : '';
                ?>
                    <div class="tab-pane fade <?= $activeClass; ?>" id="featured_section_<?= $keyType; ?>_<?= $val; ?>" role="tabpanel" aria-labelledby="tab-<?= $val; ?>">
                        <div class="row">
                            <?php foreach ($data['featured_products'] as $item): ?>
                                <?php if ($item['category'] == $c): 
                                    if ($branch_id == "" || (isset($item['branch_na']) && !in_array($branch_id , $item['branch_na']))){ ?>
                                <?= render_featured_product($item, $searchResult, $cardsToShow  , $cart_btn_status , $cart_feature_exsits , $logo,$decimal_places); ?>
                                     <?php } endif; ?>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php $jj++; endforeach; ?>
            </div>
        </div>
    </div>
    <?php
}

function render_featured_products_with_tabs($data, $keyType, $category, $searchResult , $cart_btn_status , $cart_feature_exsits , $logo, $branch_id, $decimalPlaces) {
    $cardsToShow = $data['cards'];
?>
    <div class="feature_section main_feature_parent_class_<?= $keyType?>">
        <div class="container">
            <?php if(isset($data['name']) && $data['name'] != "") { ?>
                <div class="feature_section_heading_<?= $keyType; ?>">
                    <?= $data['name']; ?>                        
                </div>
            <?php } ?>
           <div class="row custom_feature_section_parent" style='justify-content:center;'>
                <div class='custom_feature_section_figure'></div>
                  <div style='justify-content:center;' class='main_feature_<?= $keyType?> row'>
                 <div style="justify-content:center; padding:0" class="row <?= isset($data['slider']) && $data['slider'] == 1 ? "feature_section_slider_$keyType" : "" ?> feature_section_row_<?= $keyType ?>">
                <?php foreach ($data['featured_products'] as $item):
                        if ($branch_id == "" || (isset($item['branch_na']) && !in_array($branch_id , $item['branch_na']))){ ?>
                    <?= render_featured_product($item, $searchResult, $cardsToShow , $cart_btn_status , $cart_feature_exsits , $logo,$decimalPlaces); ?>
                    <?php } endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}


function render_category_section($categories) {
    $categories['design'] = preg_replace('/\$this->obw->/', '', $categories['design']);
    $featured_category_section = $categories['featured_category'];
    $featured_category_section = json_decode(json_encode($featured_category_section));
    eval('?>' . $categories['design']);
}


function convertDomainToCdnUrl2($dominUrl2,$cdnUrl2, $height, $width, $imageUrl) 
  {
    $newImageUrl = "";
    if($height != "" && $width != ""){
        $cdnUrl2 = $cdnUrl2.$width.'x'.$height.'/';
    }
    else
    {
        $cdnUrl2 = $cdnUrl2.$height.'/';
    }
    $newImageUrl = str_replace($dominUrl2, $cdnUrl2, $imageUrl);
    return $newImageUrl;
  }

