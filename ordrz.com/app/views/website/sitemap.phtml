<urlset
    xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
            http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
  <!-- Homepage -->
  <url>
    <loc>https://<?php echo $currentDomain; ?>/</loc>
    <lastmod><?php echo $today; ?></lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>


  <?php

  foreach($pages['page_slug'] as $page){ 
    ?>
    <loc><?php echo 'https://'. $_SERVER['HTTP_HOST']?>/<?php echo $page ?></loc>
        <lastmod><?php echo date('c', time());?></lastmod>
        <priority>0.80</priority>
  <?php }
  ?>
  
  <!-- Main Pages -->
  <?php foreach ($mainPages as $page): ?>
  <url>
    <loc>https://<?php echo $currentDomain . $page['url']; ?></loc>
    <lastmod><?php echo $today; ?></lastmod>
    <changefreq><?php echo $page['changefreq']; ?></changefreq>
    <priority><?php echo $page['priority']; ?></priority>
  </url>
  <?php endforeach;
    foreach ($menu_response['categories'] as $row){
    $regex = '#[^a-z0-9_]#i';
    $val = preg_replace($regex, '', $row['category_name']);
    $cid = strtolower($val);
    $cat = (string)$row['category_name'];
    ?>

        <url>
            <loc><?php echo 'https://'. $_SERVER['HTTP_HOST']?>/<?php echo $menuUrlName;?>/<?php echo isset($row['slug']) ? $row['slug'] : $cid; ?></loc>
            <lastmod><?php echo date('c', time());?></lastmod>
            <priority>0.80</priority>
        </url>

    <?php }

      foreach ($menu_response['items'] as $row){
            // $val = preg_replace($regex, '-', $row['name']);
            $val = createSlug($row['name']);
            $cid = strtolower($val);
             ?>
            <url>
                <loc><?php echo 'https://'. $_SERVER['HTTP_HOST']?>/product/<?php echo $cid.'-'.$row['menu_item_id'] ; ?></loc>
                <lastmod><?php echo date('c', time());?></lastmod>
                <priority>0.80</priority>
            </url>
        <?php } ?>

</urlset>
