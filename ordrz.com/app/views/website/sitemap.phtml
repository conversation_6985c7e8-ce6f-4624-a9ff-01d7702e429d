<?php echo '<?xml version="1.0" encoding="UTF-8"?>'; ?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <!-- Homepage -->
  <url>
    <loc>https://<?php echo $currentDomain; ?>/</loc>
    <lastmod><?php echo $today; ?></lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>

  <!-- Main Pages -->
  <?php foreach ($mainPages as $page): ?>
  <url>
    <loc>https://<?php echo $currentDomain . $page['url']; ?></loc>
    <lastmod><?php echo $today; ?></lastmod>
    <changefreq><?php echo $page['changefreq']; ?></changefreq>
    <priority><?php echo $page['priority']; ?></priority>
  </url>
  <?php endforeach; ?>

  <!-- Static Pages -->
  <?php if (isset($pages['page_slug']) && is_array($pages['page_slug'])): ?>
    <?php foreach($pages['page_slug'] as $page): ?>
    <url>
      <loc>https://<?php echo $currentDomain; ?>/<?php echo $page; ?></loc>
      <lastmod><?php echo date('c', time()); ?></lastmod>
      <priority>0.80</priority>
    </url>
    <?php endforeach; ?>
  <?php endif; ?>

  <!-- Categories -->
  <?php if (isset($menu_response['categories']) && is_array($menu_response['categories'])): ?>
    <?php foreach ($menu_response['categories'] as $row):
      $regex = '#[^a-z0-9_]#i';
      $val = preg_replace($regex, '', $row['category_name']);
      $cid = strtolower($val);
    ?>
    <url>
      <loc>https://<?php echo $currentDomain; ?>/<?php echo $menuUrlName; ?>/<?php echo isset($row['slug']) ? $row['slug'] : $cid; ?></loc>
      <lastmod><?php echo date('c', time()); ?></lastmod>
      <priority>0.80</priority>
    </url>
    <?php endforeach; ?>
  <?php endif; ?>

  <!-- Products -->
  <?php if (isset($menu_response['items']) && is_array($menu_response['items'])): ?>
    <?php foreach ($menu_response['items'] as $row):
      $regex = '#[^a-z0-9_]#i';
      $val = preg_replace($regex, '-', $row['name']);
      $cid = strtolower($val);
    ?>
    <url>
      <loc>https://<?php echo $currentDomain; ?>/product/<?php echo $cid.'-'.$row['menu_item_id']; ?></loc>
      <lastmod><?php echo date('c', time()); ?></lastmod>
      <priority>0.80</priority>
    </url>
    <?php endforeach; ?>
  <?php endif; ?>
</urlset>
