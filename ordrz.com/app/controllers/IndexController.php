<?php
declare(strict_types=1);

use Obw\Obw;
use Seo\Seo;


/**
 * IndexController class handles the main functionalities such as fetching business details,
 * SEO settings, and rendering different views based on conditions. This class extends ControllerBase.
 */
class IndexController extends ControllerBase
{
    public $obw;           // Object for handling business details
    public $seo;           // Object for handling SEO details
    public $apiController; // Object for handling API controller

    /**
     * initialize() method is called at the beginning of each controller action. It is used to set up the environment.
     */
    public function initialize(){
        // Set headers to allow cross-origin resource sharing (CORS)
        header("Access-Control-Allow-Origin: *");
        header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
        header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept");
        header("Access-Control-Allow-Credentials: true");
        header("Access-Control-Max-Age: 3600");  // Cache preflight requests for 1 hour
        
        // Initialize objects for business and SEO
        $this->obw = new Obw();
        $this->seo = new Seo();
        $this->apiController = new ApiController();

        // Get the server name (domain)
        $name = $_SERVER['SERVER_NAME'];

        // Get business details using the domain name
        $businessUsername = $this->obw->domainDetails($name);
        $this->obw = $this->obw->getBusinessDetail($businessUsername);
    }

    
    /**
     * indexAction() is the main method that handles the home page logic.
     * It fetches data, sets SEO, and decides which view to render based on business details.
     */
    public function indexAction($id = null)
    {
        // Check if the business details object is null, if so, show an error page
        $obw = $this->obw;
        if ($obw == null){
            $this->view->pick("index/partials/brokenPage");
            return;
        }

        // Fetch and set branch details if an ID is provided
        if (isset($id) && $id != null) {
            $this->fetchAndSetBranchDetails($id, $obw);
        }

        // Get SEO information based on the brand ID and the page type ("home")
        $seo = $this->seo->getSeo($obw['brand_id'], "home");
        
        // Fetch restaurant locations if no branch_id cookie is set

        // If a custom theme exists, render the theme page
        if (isset($obw['web_theme']) && $obw['web_theme'] != "") {
            $this->themeAction();
        } else {
            // Otherwise, construct the URL for fetching data and render views based on conditions
            $url = $this->constructURL($obw);
            $response = $this->getResponseFromURL($url);
            $this->view->response = $response;
            $this->pickViewBasedOnCondition($obw);
        }

        // Set business details and SEO data in the view
        $this->setBusinessDetail($obw, $seo);

        // Pass restaurant data to the view
        $this->view->resturents = $resturents ?? [];

        // Set cart feature and button status in the view
        $this->setViewParams($obw);
    }

    /**
     * setViewParams() method sets the cart feature and button status for the view.
     */
    public function setViewParams($obw){
        $this->view->cart_feature_exsits =  $obw['feature'];  // Feature information of the cart
        $this->view->cart_btn_status = $obw['status'];        // Status of the cart button
    }

    /**
     * fetchAndSetBranchDetails() method fetches details of a specific branch and sets them as cookies.
     */
    public function fetchAndSetBranchDetails($id, $obw){
        if (isset($id) && $id != "") {
            // Build the URL to fetch branch details
            $branchDetailURL = "https://" . $_SERVER['SERVER_NAME'] . "/api/restaurantLocations?business_id=" . $obw['res_id'] . "&location_id=" . $id;
            $branchdata = file_get_contents($branchDetailURL);

            // Handle possible errors in the HTTP request
            if ($branchdata === false) {
                echo "Failed to fetch branch details. Error: HTTP request failed.";
            } else {
                $branchdata = json_decode($branchdata, true);

                // Handle possible JSON decoding errors
                if ($branchdata === null) {
                    echo "Failed to decode JSON response.";
                } else {
                    // Extract branch information and set cookies for branch details
                    $branch =  $branchdata['result']['branches'][0];
                    $branchName = $branch['address'] . " " . $branch['location'] . " " . $branch['city'];
                    $branchName = urldecode($branchName);
                    $branchId = $branch["id"];
                    echo $branchName;
                    echo $branchId;

                    setcookie("branch_id", strval($branchId));
                    setcookie("branch_name", $branchName);

                    // Set cookie for order type based on the branch's delivery and pickup options
                    if (isset($branch['delivery']) && $branch['delivery'] == 1) {
                        setcookie("order_type", "delivery");
                    }
                    if (isset($branch['pickup']) && $branch['pickup'] == 1 && $branch['delivery'] != 1) {
                        setcookie("order_type", "pickup");
                    }
                }
            }
        }
    }

    /**
     * getRestaurantLocations() method retrieves the restaurant locations based on the business ID.
     */

    /**
     * setBusinessDetail() method sets the business details and SEO information in the view.
     */
    private function setBusinessDetail($obw, $seo){
        $this->view->businessDetail = $obw;  // Set business details in the view
        $this->view->title = $seo['gtitle'] ?? '';  // Set SEO title in the view
        $this->view->h1 = $seo['gh1'] ?? '';        // Set SEO H1 in the view
        $this->view->keywords = $seo['gky'] ?? '';  // Set SEO keywords in the view
        $this->view->desc = $seo['gdesc'] ?? '';    // Set SEO description in the view
    }

    /**
     * constructURL() method constructs a URL to fetch products from the API based on the business and branch ID.
     */
    private function constructURL($obw){
        // Default branch ID if no branch cookie is set
        $branch_id = (isset($_COOKIE['branch_id']) && $_COOKIE['branch_id'] != "") ?  $_COOKIE['branch_id'] : "18997";
        $name = $_SERVER['SERVER_NAME'];
        $url = "";

        // Construct the API URL to get products based on the business and branch ID
        if(isset($obw) && isset($obw['res_id'])) {
            $url = "https://uz7uygo3ui.execute-api.us-east-1.amazonaws.com/prod/api/business/" . $obw['res_id'] . "/location/" . $branch_id . "/products";

            // For staging domains, use a different API URL
            if (preg_match("/^([^.]+)\.staging\.ordrz\.com/", $name)) {
                $url = "https://beta1.tossdown.com/api/products?business_id=" . $obw['res_id'] . "&branch_id=" . $branch_id;
            }
        }
        return $url;
    }

    /**
     * getResponseFromURL() method sends an HTTP GET request to the given URL and decodes the JSON response.
     */
    private function getResponseFromURL($url){
        if (!empty($url)) {
            $instaResult = file_get_contents($url);
            return json_decode($instaResult, true);
        } else {
            return [];
        }
    }

    /**
     * pickViewBasedOnCondition() method determines which view to render based on the presence of business details.
     */
    private function pickViewBasedOnCondition($obw){
        if(isset($obw) && $obw != ""){
            $this->view->pick('index/home');  // Render the home page view if business details exist
        } else {
            $this->view->pick('index/error');  // Render an error page if business details are missing
        }
    }

    private function send_grid_email_api($data)
        {
            $sendgrid_apikey = SENDGRIDKEY;
            $sendgrid = new SendGrid($sendgrid_apikey);
            $to = $data['to'];
            $from = $data['from'];
            if (str_contains($from, '.fr')) {
                return false;
            }
            $fromname = $data['fromname'];
            $subject = $data['subject'];
            $text = '';
            $html = $data['html'];
            $url = 'https://api.sendgrid.com/';
            $pass = $sendgrid_apikey;
            $params = array(
            'to'        => $to,
            'from'      => $from,
            'fromname'  => $fromname,
            'subject'   => $subject,
            'text'      => $text,
            'html'      => $html,
            );
            $request =  $url . 'api/mail.send.json';


            $session = curl_init($request);
            // Tell PHP not to use SSLv3 (instead opting for TLS)
            curl_setopt($session, CURLOPT_SSLVERSION, 'CURL_SSLVERSION_TLSv1_2');
            curl_setopt($session, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $sendgrid_apikey));
            // Tell curl to use HTTP POST
            curl_setopt($session, CURLOPT_POST, true);
            // Tell curl that this is the body of the POST
            curl_setopt($session, CURLOPT_POSTFIELDS, $params);
            // Tell curl not to return headers, but do return the response
            curl_setopt($session, CURLOPT_HEADER, false);
            curl_setopt($session, CURLOPT_RETURNTRANSFER, true);
            // obtain response
            $response = curl_exec($session);
            curl_close($session);
            $response = json_decode($response);
            if ($response->message == 'success') {
            return true;
            } else {
            return false;
            }
        }

    /**
     * paymentConfirmationAction() handles payment confirmations.
     * It checks the payment status and renders appropriate views.
     */
    public function paymentConfirmationAction($gateway){
        $obw = $this->obw;
        if ($gateway == "paymob") {
            $this->paymentConfirmationByPaymob($obw);
        } elseif ($gateway == "payfast") {
            $this->paymentConfirmationByPayfast($obw);
        }
    }

    private function paymentConfirmationByPaymob($obw)
    {
        $requestPaymob = $this->request->getQuery();
        $merchantId = $requestPaymob['merchant_order_id'];
        $status = $requestPaymob['success'];
        $txnResponseCode = $requestPaymob['txn_response_code'];
        $order_id = explode("-" , $requestPaymob['merchant_order_id']);
        $order_id = $order_id[0]; 
        $this->view->setVar('data', $order_id); 
        $obw = $this->obw;
        $this->view->setVar('businessDetail', $obw); 
        
        if ($status == true && $txnResponseCode == "APPROVED") {
            $this->view->pick('index/thankyou');
        } else {
            $this->view->pick('index/transectiondecline');
        }
    }



    private function paymentConfirmationByPayfast($obw)
    {
        $requestPayfast = $this->request->getQuery();
        $error_code = $requestPayfast['err_code'];
        $order_id = $requestPayfast['order_id'];
        $res_id = $obw['res_id'];
        $clientName = $obw['name'];
        $bid = (isset($_COOKIE['branch_id']) && $_COOKIE['branch_id'] != "") ?  $_COOKIE['branch_id'] : "";
        $dataSend = array();
        $url = $_SERVER['REQUEST_URI']; 
        $dataSend['to'] = "<EMAIL>";
        $dataSend['from'] = "<EMAIL>";
        $dataSend['fromname'] = "Tossdown";
        $dataSend['subject'] = "Payfast Log - Order # " . $order_id . " - " . $clientName;
        $requestDetails = "<pre>" . htmlspecialchars(print_r($requestPayfast, true)) . "</pre>";
        $dataSend['html'] = print_r(array(
            "QueryParams" => $requestDetails,
            "URL" => htmlspecialchars($url) 
        ), true);
        $this->send_grid_email_api($dataSend);
        $obw = $this->obw;
        $this->view->data = $requestPayfast;
        $this->view->businessDetail = $obw;
        if ($error_code === "000" || $error_code === "00") {
            $url = "https://tossdown.com/api/order_status_update";
            $querystrng = "status=Confirmed&order_id=" . $order_id . "&eatout_id=" . $res_id . "&eatout_uid=649559"."&source=web&payment_type=1&bid=".$bid;
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $querystrng);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $server_output = curl_exec($ch);
            $xmlOrder = $server_output;
            curl_close($ch);
            $this->view->pick('index/thankyou');
        } else {
            $this->view->pick('index/transectiondecline');
        }
    }

    /**
     * themeAction() renders the business theme, if a custom theme is available.
     */
    public function themeAction(){
        $obw = $this->obw;
        $this->view->businessDetail = $obw;
        $this->view->resturent_details = $obw;
        $this->view->decimal_places = $obw['decimal_places'];

        // Decode and set web and mobile themes in the view if they exist
        if (isset($obw['web_theme']) && $obw['web_theme'] != "") {
            $this->view->web_theme = json_decode($obw['web_theme'], true);
        }
        if (isset($obw['mobile_theme']) && $obw['mobile_theme'] != "") {
            $this->view->mobile_theme = json_decode($obw['mobile_theme'], true);
        }

        // Render the theme view
        $this->view->pick('theme/index');
    }

    /**
     * staticPageAction() retrieves and renders a static page based on the given slug.
     */
    public function staticPageAction($slug) {
        $obw = $this->obw;
        $slug = json_encode($slug);

        // Build the URL to fetch the static page content
        $url = SUPER_PATH . "get_static_page?res_id=" . $obw['res_id'] . "&page_slug=" . $slug;
        $response = file_get_contents($url);

        // Handle possible errors in the HTTP request
        if ($response === false) {
            echo "Error fetching the static page.";
        }

        // Decode the JSON response
        $data = json_decode($response, true);

        // Check for JSON decoding errors and render an error page if any occur
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->view->pick('index/error');
        }

        // Set business details, themes, and other data in the view
        $this->view->businessDetail = $obw;
        if (isset($obw['web_theme']) && $obw['web_theme'] != "") {
            $this->view->web_theme = json_decode($obw['web_theme'], true);
        }
        if (isset($obw['mobile_theme']) && $obw['mobile_theme'] != "") {
            $this->view->mobile_theme = json_decode($obw['mobile_theme'], true);
        }
        //If static page not exists. 
        if(empty($data)){
            $this->view->pick('index/error');
            return;
        }
        $staticPage = true;
        $pageSeo = json_decode($data['result']['page_seo'],true);

        // Get SEO data from page
        $seoData = [];
        if (!empty($data['result']['page_seo'])) {
            $pageSeo = json_decode($data['result']['page_seo'], true);
            $seoData = $pageSeo['original'] ?? [];
        }

        // Set SEO values with fallbacks
        $this->view->title = $seoData['page_title'] ?? $obw['name'] ?? '';
        $this->view->h1 = $seoData['page_h1'] ?? $seoData['page_title'] ?? $obw['name'] ?? '';
        $this->view->keywords = $seoData['page_keywords'] ?? $obw['name'] ?? '';
        $this->view->desc = $seoData['page_description'] ?? $obw['description'] ?? $obw['name'] ?? '';

        // Set business details and theme data in the view
        $this->view->theme_setting = json_decode($obw['theme_settings']);
        $this->view->cart_feature_exsits =  $obw['feature'];
        $this->view->cart_btn_status = $obw['status'];
        $this->view->content = $data;
        $this->view->staticPage = $staticPage;
        // Render the static page view if data is available; otherwise, render an error page
        if(isset($data['result']) && $data != ""){
            $this->view->pick('index/staticpage');
        }else{
            $this->view->pick('index/error');
        }
    }

    /**
     * locationsAction() method retrieves the business locations and renders them in a view.
     */
    public function locationsAction(){
        $obw = $this->obw;

        // Build the URL to fetch all business locations
        $business_id = $obw['res_id'];
        $url = "https://d9gwfwdle3.execute-api.us-east-1.amazonaws.com/prod/v1/business/".$business_id."/locations";
        $resturents = file_get_contents($url);

        // Decode the response and set the data in the view
        $response = json_decode($resturents);
        $this->view->data = $response->result->branches;

        // Set business details and theme data in the view
        $this->view->businessDetail = $obw;
        $this->view->cart_feature_exsits =  $obw['feature'];
        $this->view->cart_btn_status = $obw['status'];
        if (isset($obw['web_theme']) && $obw['web_theme'] != "") {
            $this->view->web_theme = json_decode($obw['web_theme'], true);
        }
        if (isset($obw['mobile_theme']) && $obw['mobile_theme'] != "") {
            $this->view->mobile_theme = json_decode($obw['mobile_theme'], true);
        }

        // Render the locations view
        $this->view->pick('index/locations');
    }
}