<?php

declare(strict_types=1);

use Obw\Obw;
use Seo\Seo;

class WebsiteController extends ControllerBase
{
  public $obw;
  public $seo;
  public $apiController;

  public function initialize()
  {
    header("Access-Control-Allow-Origin: *");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Allow-Headers: Origin, X-Requested-With, Content-Type, Accept");
    header("Access-Control-Allow-Credentials: true");
    header("Access-Control-Max-Age: 3600");
    $this->obw = new Obw();
    $this->seo = new Seo();
    $name = $_SERVER['SERVER_NAME'];
    $businessUsername = $this->obw->domainDetails($name);
    $this->obw = $this->obw->getBusinessDetail($businessUsername);
  }
  private function send_grid_email_api($data)
  {
    $sendgrid_apikey = SENDGRIDKEY;
    $sendgrid = new SendGrid($sendgrid_apikey);
    $to = $data['to'];
    $from = $data['from'];
    if (str_contains($from, '.fr')) {
        return false;
    }
    $fromname = $data['fromname'];
    $subject = $data['subject'];
    $text = '';
    $html = $data['html'];
    $url = 'https://api.sendgrid.com/';
    $pass = $sendgrid_apikey;
    $params = array(
      'to'        => $to,
      'from'      => $from,
      'fromname'  => $fromname,
      'subject'   => $subject,
      'text'      => $text,
      'html'      => $html,
    );
    $request =  $url . 'api/mail.send.json';


    $session = curl_init($request);
    // Tell PHP not to use SSLv3 (instead opting for TLS)
    curl_setopt($session, CURLOPT_SSLVERSION, 'CURL_SSLVERSION_TLSv1_2');
    curl_setopt($session, CURLOPT_HTTPHEADER, array('Authorization: Bearer ' . $sendgrid_apikey));
    // Tell curl to use HTTP POST
    curl_setopt($session, CURLOPT_POST, true);
    // Tell curl that this is the body of the POST
    curl_setopt($session, CURLOPT_POSTFIELDS, $params);
    // Tell curl not to return headers, but do return the response
    curl_setopt($session, CURLOPT_HEADER, false);
    curl_setopt($session, CURLOPT_RETURNTRANSFER, true);
    // obtain response
    $response = curl_exec($session);
    curl_close($session);
    $response = json_decode($response);
    if ($response->message == 'success') {
      return true;
    } else {
      return false;
    }
  }
  public function newContactUsAction() 
  {
    if (!$this->request->isPost()) {
        $this->session->set('status', "error");
        $this->session->set('message', "Invalid request method");
        return header("Location: " . "/");

    }  

    // Basic spam prevention checks
    $email = isset($_POST['email']) ? filter_var($_POST['email'], FILTER_SANITIZE_EMAIL) : "";
    $fullname = isset($_POST['fullname']) ? trim($_POST['fullname']) : "";
    $message = isset($_POST['message']) ? trim($_POST['message']) : "";

    // Validate email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $this->session->set('status', "error");
        $this->session->set('message', "Invalid email address");
        return header("Location: " . "/");
    }

    // Block common spam domains
    $blockedDomains = ['temp-mail.org', 'tempmail.com', '.xyz', '.top', '.monster', '.buzz','sympatico.ca'];
    $emailDomain = substr(strrchr($email, "@"), 1);
    foreach ($blockedDomains as $blockedDomain) {
        if (stripos($emailDomain, $blockedDomain) !== false) {
            $this->session->set('status', "error");
            $this->session->set('message', "This email domain is not allowed");
            return header("Location: " . "/");
        }
    }
   


    // Check for spam keywords in message
    $spamKeywords = ['viagra', 'casino', 'lottery', 'winner', 'buy now', 'discount'];
    foreach ($spamKeywords as $keyword) {
        if (stripos($message, $keyword) !== false) {
            $this->session->set('status', "error");
            $this->session->set('message', "Your message contains prohibited content");
            return header("Location: " . "/");
          }
    }

    // Rate limiting - use session to track submissions
    $lastSubmission = $this->session->get('last_contact_submission', 0);
    $currentTime = time();
    if ($currentTime - $lastSubmission < 300) { // 5 minutes cooldown
        $this->session->set('status', "success");
        $this->session->set('message', "Please wait 5 minutes before sending another message");
        $this->session->set('display', "true");
        return header("Location: " . "/");
      
    }
    $this->session->set('last_contact_submission', $currentTime);

    // If all validation passes, proceed with the original function logic
    $obw = $this->obw;
    $theme = isset($obw['theme_settings']) && $obw['theme_settings'] != "" ? json_decode($obw['theme_settings']) : "";
    $theme_settings = $theme != "" && isset($theme->theme_settings) && $theme->theme_settings != "" ? $theme->theme_settings : "";
    $to_email = $theme_settings && $theme_settings->contact_us_email_address ? $theme_settings->contact_us_email_address : "";
    $data = [];
    $data['email'] = isset($_POST['email']) ? $_POST['email'] : "";
    $data['fullname'] = isset($_POST['fullname']) ? $_POST['fullname'] : "";
    $data['name'] = isset($_POST['name']) ? $_POST['name'] : "";
    $data['country'] = isset($_POST['country']) ? $_POST['country'] : '';
    $data['city'] = isset($_POST['city']) ? $_POST['city'] : '';
    $data['mobile'] = isset($_POST['mobile']) ? $_POST['mobile'] : '';
    $data['province'] = isset($_POST['province']) ? $_POST['province'] : '';
    $data['whatsapp'] = isset($_POST['whatsapp']) ? $_POST['whatsapp'] : '';
    $data['postal_code'] = isset($_POST['postal_code']) ? $_POST['postal_code'] : '';
    $data['message'] = isset($_POST['message']) ? $_POST['message'] : '';
    $data['inquiry'] = isset($_POST['inquiry']) ? $_POST['inquiry'] : '';
    $data['quality'] = isset($_POST['f_quality']) ? $_POST['f_quality'] : '';
    $data['preferred_region'] = isset($_POST['preferred_region']) ? $_POST['preferred_region'] : '';
    $data['promo_page'] = 0;
    $data['cash_in_hand'] = isset($_POST['cash_in_hand']) ? $_POST['cash_in_hand'] : '';
    $data['operator'] = isset($_POST['operator']) ? $_POST['operator'] : '';
    $data['quantity'] = isset($_POST['f_quantity']) ? $_POST['f_quantity'] : '';
    $data['kb_catering_menu'] = isset($_POST['kb_catering_menu']) ? $_POST['kb_catering_menu'] : '';
    $data['service'] = isset($_POST['f_service']) ? $_POST['f_service'] : '';
    $data['value_for_money'] = isset($_POST['f_value_for_money']) ? $_POST['f_value_for_money'] : '';
    $data['suggestions'] = isset($_POST['suggestions']) ? $_POST['suggestions'] : '';
    $data['boxcount'] = isset($_POST['boxcount']) ? $_POST['boxcount'] : '';
    $data['company'] = isset($_POST['company']) ? $_POST['company'] : '';
    $data['fav_items'] = isset($_POST['fav_items']) ? implode(", ", $_POST['fav_items']) : '';
    $data['new_item_suggestions'] = isset($_POST['new_item_suggestions']) ? implode(", ", $_POST['new_item_suggestions']) : '';
    $data['found_us_at'] = isset($_POST['found_us_at']) ? implode(",", $_POST['found_us_at']) : '';
    $data['custom_datentime'] = isset($_POST['date_time']) ? str_replace("T", " ", $_POST['date_time']) : '';
    $data['food_service'] = isset($_POST['food_service']) ? $_POST['food_service'] : '';
    $data['satisfaction'] = isset($_POST['satisfaction']) ? $_POST['satisfaction'] : '';
    $data['variety'] = isset($_POST['m_variety']) ? $_POST['m_variety'] : '';
    $data['atmosphere'] = isset($_POST['f_atmosphere']) ? $_POST['f_atmosphere'] : '';
    $data['cleanliness'] = isset($_POST['m_cleanliness']) ? $_POST['m_cleanliness'] : '';
    $data['experience'] = isset($_POST['o_experience']) ? $_POST['o_experience'] : '';
    $data['family_member'] = isset($_POST['family_member']) ? $_POST['family_member'] : '';
    $data['quantity'] = isset($_POST['quantity']) ? $_POST['quantity'] : '';
    $data['address'] = isset($_POST['address']) ? $_POST['address'] : '';
    $data['package'] = isset($_POST['package']) ? $_POST['package'] : '';
    $data['branch'] = isset($_POST['branch']) ? $_POST['branch'] : '';
    $data['device'] = isset($_POST['device']) ? $_POST['device'] : '';
    $data['interest'] = isset($_POST['interest']) ? $_POST['interest'] : '';
    $data['model_no'] = isset($_POST['model_no']) ? $_POST['model_no'] : '';
    $data['designation'] = isset($_POST['designation']) ? $_POST['designation'] : '';
    $data['upload'] = isset($_POST['upload']) ? $_POST['upload'] : '';
    $data['file'] = isset($_POST['file']) ? $_POST['file'] : '';
    $data['phone'] = isset($_POST['phone']) ? $_POST['phone'] : '';
    $data['know_about'] = isset($_POST['know_about']) ? $_POST['know_about'] : '';
    $data['rental_food'] = isset($_POST['rental_food']) ? $_POST['rental_food'] : '';
    $data['managing_franchise'] = isset($_POST['managing_franchise']) ? $_POST['managing_franchise'] : '';
    $data['interested_opportunity'] = isset($_POST['interested_opportunity']) ? $_POST['interested_opportunity'] : '';
    $data['physical_site'] = isset($_POST['physical_site']) ? $_POST['physical_site'] : '';
    $data['owned_rented'] = isset($_POST['owned_rented']) ? $_POST['owned_rented'] : '';
    $data['yes_details'] = isset($_POST['yes_details']) ? $_POST['yes_details'] : '';
    $data['size_of_store'] = isset($_POST['size_of_store']) ? $_POST['size_of_store'] : '';
    $data['financial_resouces'] = isset($_POST['financial_resouces']) ? $_POST['financial_resouces'] : '';
    $data['capital_invest'] = isset($_POST['capital_invest']) ? $_POST['capital_invest'] : '';

    $data['issue'] = isset($_POST['issue']) ? $_POST['issue'] : '';
    $data['interest'] = isset($_POST['interested']) ? $_POST['interested'] : '';
    $data['business_name'] = isset($_POST['business_name']) ? $_POST['business_name'] : '';
    $data['cmplt_date'] = isset($_POST['cmplt_date']) ? $_POST['cmplt_date'] : '';
    $data['price_range'] = isset($_POST['price_range']) ? $_POST['price_range'] : '';
    $data['dimensions'] = isset($_POST['dimensions']) ? $_POST['dimensions'] : '';
    $data['description'] = isset($_POST['description']) ? $_POST['description'] : '';
    $data['product'] = isset($_POST['product']) ? $_POST['product'] : '';
    $data['product_category'] = isset($_POST['product_category']) ? $_POST['product_category'] : '';
    $data['flavour'] = isset($_POST['flavour']) ? $_POST['flavour'] : '';
    $data['weight'] = isset($_POST['weight']) ? $_POST['weight'] : '';
    $data['num_guest'] = isset($_POST['num_guest']) ? $_POST['num_guest'] : '';
    $data['event_type'] = isset($_POST['event_type']) ? $_POST['event_type'] : '';
    $data['event_date'] = isset($_POST['event_date']) ? $_POST['event_date'] : '';
    $data['venue'] = isset($_POST['venue']) ? $_POST['venue'] : '';
    $data['time_slot'] = isset($_POST['time_slot']) ? $_POST['time_slot'] : '';
    $data['furniture_bazar_campaign'] = isset($_POST['furniture_bazar_campaign']) ? $_POST['furniture_bazar_campaign'] : '';

    if (isset($_FILES['resume']['name'])) {
      $uploadResponse = uploadFiles($_FILES['resume']); 
      if ($uploadResponse && isset($uploadResponse[0]['fileUrl'])) {
          $data['file'] = $uploadResponse[0]['fileUrl'];
      }
  } 
    if ($_FILES['file']['name']) {
      $image_link = $this->seo->uploadToS3($_FILES['file']['tmp_name'], $_FILES['file']['type'], basename($_FILES['file']['name']));
      $data['upload'] = $image_link;
    }
    $message = isset($_POST['custom_message']) && trim($_POST['custom_message']) !== "" 
    ? $_POST['custom_message'] 
    : "We have received your message and will respond to you within 5 business days. Thank you.";
    $this->view->setVars($data);
    $this->view->render('index', 'contactusemail');
    $msg = $this->view->getContent();
    $dataSend = array();
    $dataSend['to'] = isset($to_email) && $to_email != "" ? $to_email : $_POST['to_email'];
    $dataSend['from'] = $_POST['email'];
    $dataSend['fromname'] = isset($_POST['fullname']) ? $_POST['fullname'] : "";
    $dataSend['subject'] = isset($obw) && isset($obw['name']) ? $obw['name'] : '';
    $dataSend['html'] = $msg;
    $this->send_grid_email_api($dataSend);
    $this->session->set('status', "success");
    $this->session->set('message', $message);
    $this->session->set('display', "true");
    header("Location: " . "/");
  }

  public function reservetableAction()
  {
    $redirectURL = filter_input(INPUT_POST, 'redirect_url', FILTER_SANITIZE_URL);
    $data = [
      'email' => filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL),
      'name' => filter_input(INPUT_POST, 'name', FILTER_SANITIZE_SPECIAL_CHARS),
      'date' => filter_input(INPUT_POST, 'res_date', FILTER_SANITIZE_SPECIAL_CHARS),
      'time' => filter_input(INPUT_POST, 'res_time', FILTER_SANITIZE_SPECIAL_CHARS),
      'guest' => filter_input(INPUT_POST, 'guest', FILTER_SANITIZE_NUMBER_INT),
      'title' => 'Reserve A Table | ',
      'desc' => '',
      'keywords' => ''
    ];
    $timestamp = strtotime($data['date']);
    $data['day'] = date('l', $timestamp);
    $bid = filter_input(INPUT_POST, 'bid', FILTER_SANITIZE_SPECIAL_CHARS);
    $branch = explode('$', $bid);
    $branchID = $branch[0];
    $branchPhone = $branch[1] ?? '';
    $_POST['bid'] = $branchID;
    $branchEmail = filter_input(INPUT_POST, 'branch_email', FILTER_SANITIZE_EMAIL);
    $url = API_PATH . "app_save_res";
    $postData = http_build_query($_POST);
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $serverOutput = curl_exec($ch);
    curl_close($ch);

    // Handle API response
    if (preg_match('/You have already made a reservation./', $serverOutput)) {
      $message = '<div class="alert alert-danger">You have already made a reservation. For more information, please contact us at ' .
        $branchPhone . ' or email us at ' . $branchEmail . '</div>';
      $this->session->set("message", $message);
      header("Location:  /");
    } else {
      $message = 'For more information, please contact us at ' . $branchPhone . ' or email us at ' . $branchEmail;
      $this->session->set("message", $message);
      header("Location:  /");
    }
  }
  public function bankAlfalahAction($orderId)
  {
    $requestPayfast = $this->request->getQuery();
    $res_id = $this->obw['res_id'];
    $bid = (isset($_COOKIE['branch_id']) && $_COOKIE['branch_id'] != "") ?  $_COOKIE['branch_id'] : "";
    $eatout_uid = $requestPayfast['eatout_uid'];
    $obw = $this->obw;
    $this->view->data = $requestPayfast;
    $this->view->businessDetail = $obw;
      if($orderId != "") {
        $url = "https://tossdown.com/api/order_status_update";
        $querystrng = "status=Confirmed&order_id=" . $orderId . "&eatout_id=" . $res_id . "&eatout_uid=".$eatout_uid."&source=web&payment_type=1&bid=".$bid;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $querystrng);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $server_output = curl_exec($ch);
        $xmlOrder = $server_output;
        curl_close($ch);
        $this->view->pick('index/thankyou');
    } else {
        $this->view->pick('index/transectiondecline');
    }

  }
  public function accountAction()
{
  $request = $this->request->getJsonRawBody(true);
  $action = $request['actionType'];
  $email = $request['email'];
  $password = $request['password'];
  if ($action == 'login') {
    $response = $this->handleLogin($email, $password, $action);
    return $this->response->setJsonContent($response); 
  } elseif ($action == 'signup') {
    $fullname = $request['fullname'];
    $contact = $request['contact'];
    $response = $this->handleSignUp($email, $password, $fullname, $contact, $action);
    return $this->response->setJsonContent($response); 
  }
  return $this->response->setJsonContent(['success' => false, 'message' => 'Invalid action type']);
}
public function handleLogin($email, $password, $action)
{
  $obw = $this->obw;
  $apiUrl = "https://td0c8x9qb3.execute-api.us-east-1.amazonaws.com/prod/v1/business/".  $obw['res_id'] . "/user/login"; // You need to set the correct API URL here
  $payload = [
    'email' => $email,
    'password' => $password,
    'source' => 'web',
    'device' => 'web',
    'address' => '',
    'apple_id' => '',
    'city' => '',
    'dob' => '',
    'facebook_id' => '',
    'gender' => '',
    'google_id' => '',
    'token' => '',
  ];
  $response = $this->handleApiRequest($apiUrl, $payload, $action);
  return $response;
}
public function handleSignUp($email, $password, $fullname, $contact, $action)
{
  $obw = $this->obw;
  $apiUrl = "https://td0c8x9qb3.execute-api.us-east-1.amazonaws.com/prod/v1/business/".  $obw['res_id'] . "/user/signup"; // You need to set the correct API URL here
  $payload = [
    'email' => $email,
    'name' => $fullname,
    'phone' => (string) $contact,
    'password' => $password,
    'source' => 'web',
    'device' => 'web',
    'address' => '',
    'apple_id' => '',
    'city' => '',
    'dob' => '',
    'facebook_id' => '',
    'gender' => '',
    'google_id' => '',
    'token' => '',
  ];
  $response = $this->handleApiRequest($apiUrl, $payload, $action);
  return $response;
}

private function handleApiRequest($apiUrl, $payload, $action)
{
  // Check if the API URL is provided
  if (empty($apiUrl)) {
    return ['success' => false, 'message' => 'API URL is missing'];
  }
  $ch = curl_init();
  curl_setopt($ch, CURLOPT_URL, $apiUrl);
  curl_setopt($ch, CURLOPT_POST, true);
  curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
  curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
  ]);
  curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
  $response = curl_exec($ch);
  if (curl_errno($ch)) {
    $error = curl_error($ch);
    curl_close($ch);
    return ['success' => false, 'message' => 'Request error: ' . $error];
  }
  curl_close($ch);
  $decodedResponse = json_decode($response, true);
  if (isset($decodedResponse['status'])) {
    if ($decodedResponse['status'] === 200) {
      $userData = $decodedResponse['result'];
      $this->handleCookieMapping($userData);
      return [
        'success' => true,
        'message' => ucfirst($action) . ' successful.',
        'user_data' => $userData,
      ];
    } else {
      return [
        'success' => false,
        'message' => $decodedResponse['message'],
      ];
    }
  }
  return ['success' => false, 'message' => 'Unknown error'];
}

public function handleCookieMapping($result)
{
  $tdUDetails = [
    'name' => $result['user_fullname'],
    'email' => $result['user_email'],
    'phone' => $result['user_cphone'],
    'userId' => $result['user_id'],
    'td_user_id' => $result['td_user_id'],
    'authToken' => $result['token'],
    'renewAuthToken' => $result['refresh_token'],
  ];
  setcookie('tdUDetails', json_encode($tdUDetails), time() + (86400 * 7), "/");
}



  public function newsletterAction()
    {

      $obw = $this->obw;

      $res_id = $obw['res_id'];
      $email = $this->request->getPost('email');
      $curl = curl_init();
      
      curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://td0c8x9qb3.execute-api.us-east-1.amazonaws.com/prod/v1/business/' . $res_id . '/newsletter/subscribe',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => json_encode([
          'email' => $email
      ]),
        CURLOPT_HTTPHEADER => array(
          'Content-Type: application/json'
        ),
      ));

      $response = curl_exec($curl);
      curl_close($curl);

      $data = json_decode($response, true); // true returns an associative array

      // Access the status
      $status = $data['status'];
      $message = $data['message'];

      $this->session->set('status', "success");
      $this->session->set('message', $message);
      $this->session->set('display', "true");
      header("Location: " . "/");

    }

  // Serving robots.txt from the controller because URL restrictions are configured per business.
  // These settings are managed under theme settings at tossdown.site and can be accessed via:
  // tossdown.site/brand/google_webmaster

    public function robotsAction(){
      // Get the business details
      $obw = $this->obw;
      $domainFlag = isset($obw) && isset($obw['domain']) && $obw['domain'] != "";
      $currentDomain = $_SERVER['HTTP_HOST'];

      // Set the content type header
      header('Content-Type: text/plain; charset=UTF-8');

      // Start robots.txt content
      echo "User-agent: *\n";

      // If it's a custom domain and not ordrz.com, allow indexing
      if (!$domainFlag || strpos($currentDomain, 'ordrz.com') === false) {
          echo "Allow: /\n";
          echo "Disallow: /admin/\n";
          echo "Disallow: /private/\n";
          echo "Disallow: /includes/\n";
          echo "Disallow: /temp/\n";
          echo "Disallow: /cgi-bin/\n";
          echo "Disallow: /app/\n";
          echo "Disallow: /vendor/\n";
          echo "Disallow: /cache/\n\n";

          echo "# Disallow specific file types\n";
          echo "Disallow: /*.php$\n";
          echo "Disallow: /*.js$\n";
          echo "Disallow: /*.inc$\n";
          echo "Disallow: /*.css$\n\n";

          echo "# Allow CSS, JS, and images that are meant to be public\n";
          echo "Allow: /public/css/\n";
          echo "Allow: /public/js/\n";
          echo "Allow: /public/images/\n\n";

          echo "# Allow specific pages\n";
          echo "Allow: /shop\n";
          echo "Allow: /menu\n";
          echo "Allow: /brands\n";
          echo "Allow: /location\n";
          echo "Allow: /profile\n";
          echo "Allow: /checkout\n";
          echo "Allow: /product/\n\n";

          // Add sitemap reference if domain is custom
          echo "# Sitemap location\n";
          echo "Sitemap: https://" . $currentDomain . "/sitemap.xml";
      } else {
          // For ordrz.com domains, disallow all indexing
          echo "Disallow: /\n";
      }

      exit;
    }

    public function sitemapAction(){
      // Get the business details
      $obw = $this->obw;
      $domainFlag = isset($obw) && isset($obw['domain']) && $obw['domain'] != "";
      $currentDomain = $_SERVER['HTTP_HOST'];

      // Only generate sitemap for custom domains, not for ordrz.com domains
      if (!$domainFlag || strpos($currentDomain, 'ordrz.com') !== false) {
          header("HTTP/1.0 404 Not Found");
          exit;
      }

      // Set the content type header for XML
      header('Content-Type: application/xml; charset=UTF-8');

      $theme_settings = json_decode($obw['theme_settings'], true);
      // $menuUrlName = $theme_settings['theme_settings']['menu_url_name'];
      $menuUrlName = ($theme_settings['theme_settings']['shop_page_on_off'] == 1) ? "shop" : "menu";
      // Get current date for lastmod
      $today = date('Y-m-d');
      // static pages
      $static_page = SUPER_PATH . "/get_all_static_pages?restaurant=".$obw['domain']."&flag=1";
      $static_page = file_get_contents($static_page);
      $pages = json_decode($static_page, true);

      // products url
      $menu_url = API_PATH . "products?business_id=".$obw['res_id']."&attributes=1&branch_id=0&display_source=2&menu_type_id=0";
      $menu_url = file_get_contents($menu_url);
      $menu_response = json_decode($menu_url, true);

      // Main pages
      $mainPages = [
          ['url' => '/shop', 'priority' => '0.8', 'changefreq' => 'weekly'],
          ['url' => '/menu', 'priority' => '0.8', 'changefreq' => 'weekly'],
          ['url' => '/brands', 'priority' => '0.7', 'changefreq' => 'monthly'],
          ['url' => '/location', 'priority' => '0.7', 'changefreq' => 'monthly'],
          ['url' => '/profile', 'priority' => '0.6', 'changefreq' => 'monthly'],
      ];

      // Start XML output
      echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
      echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

      // Homepage
      echo '  <url>' . "\n";
      echo '    <loc>https://' . $currentDomain . '/</loc>' . "\n";
      echo '    <lastmod>' . $today . '</lastmod>' . "\n";
      echo '    <changefreq>weekly</changefreq>' . "\n";
      echo '    <priority>1.0</priority>' . "\n";
      echo '  </url>' . "\n";

      // Main pages
      foreach ($mainPages as $page) {
          echo '  <url>' . "\n";
          echo '    <loc>https://' . $currentDomain . $page['url'] . '</loc>' . "\n";
          echo '    <lastmod>' . $today . '</lastmod>' . "\n";
          echo '    <changefreq>' . $page['changefreq'] . '</changefreq>' . "\n";
          echo '    <priority>' . $page['priority'] . '</priority>' . "\n";
          echo '  </url>' . "\n";
      }

      // Static Pages
      if (isset($pages['result']['page_slug']) && is_array($pages['result']['page_slug'])) {
          foreach($pages['result']['page_slug'] as $page) {
              echo '  <url>' . "\n";
              echo '    <loc>https://' . $currentDomain . '/' . $page . '</loc>' . "\n";
              echo '    <lastmod>' . date('c', time()) . '</lastmod>' . "\n";
              echo '    <priority>0.80</priority>' . "\n";
              echo '  </url>' . "\n";
          }
      }

      // Categories
      if (isset($menu_response['categories']) && is_array($menu_response['categories'])) {
          foreach ($menu_response['categories'] as $row) {
              $regex = '#[^a-z0-9_]#i';
              $val = preg_replace($regex, '', $row['category_name']);
              $cid = strtolower($val);
              echo '  <url>' . "\n";
              echo '    <loc>https://' . $currentDomain . '/' . $menuUrlName . '/' . (isset($row['slug']) ? $row['slug'] : $cid) . '</loc>' . "\n";
              echo '    <lastmod>' . date('c', time()) . '</lastmod>' . "\n";
              echo '    <priority>0.80</priority>' . "\n";
              echo '  </url>' . "\n";
          }
      }

      // Products
      if (isset($menu_response['items']) && is_array($menu_response['items'])) {
          foreach ($menu_response['items'] as $row) {
              $regex = '#[^a-z0-9_]#i';
              $val = preg_replace($regex, '-', $row['name']);
              $cid = strtolower($val);
              echo '  <url>' . "\n";
              echo '    <loc>https://' . $currentDomain . '/product/' . $cid . '-' . $row['menu_item_id'] . '</loc>' . "\n";
              echo '    <lastmod>' . date('c', time()) . '</lastmod>' . "\n";
              echo '    <priority>0.80</priority>' . "\n";
              echo '  </url>' . "\n";
          }
      }

      // Close XML
      echo '</urlset>';
      exit;
    }
}
