<?php
declare(strict_types=1);
use Obw\Obw;
use Seo\Seo;

class ShopPageController extends ControllerBase
{
    public $obw;
    public $seo;
    public $apiController;
    
    public function initialize(){
        $this->obw = new Obw(); // Create new Obw instance
        $this->seo = new Seo(); // Create new Seo instance
        $this->apiController = new ApiController(); // Create new ApiController instance
        $name = $_SERVER['SERVER_NAME']; // Get the current server name
        $businessUsername = $this->obw->domainDetails($name); // Get business username based on the domain
        $this->obw = $this->obw->getBusinessDetail($businessUsername); // Get business details
    }
    public function shopPageAction()
    {
        $this->pageAction('shop', 'menu', 0); // Shop page
    }
    public function menuPageAction()
    {    
        $this->pageAction('menu', 'shop', 1); // Menu page
    }
    // Action for handling shop filters with a slug
    public function ShopFiltersAction($slug){
        $this->pageAction('shop', 'menu', 0 , $slug); // Shop page
    }
    // Action for handling menu filters with a slug
    public function MenuFiltersAction($slug){
        $this->pageAction('menu', 'shop', 1 , $slug); // Menu page
    }
    public function pageAction($currentPage, $targetPage, $flag , $slug = null)
    {
        $this->checkAndRedirect($currentPage, $targetPage, $flag); // Redirect if necessary
        $this->shopPageLogic($slug); // Execute shop page logic
    }
    private function checkAndRedirect($currentPage, $targetPage, $flag)
    {
        $obw = $this->obw;
        if (isset($obw['theme_settings'])) {
            $decodethemeSetting = json_decode($obw['theme_settings'], true); // Decode theme settings
            $themeSetting = $decodethemeSetting['theme_settings'];
            // Check if shop page is on/off based on flag and redirect accordingly
            if (isset($themeSetting) && isset($themeSetting['shop_page_on_off']) && $themeSetting['shop_page_on_off'] == $flag)
            {
                $requestUri = $_SERVER['REQUEST_URI']; // Get current request URI
                $newUrl = str_replace($currentPage, $targetPage, $requestUri); // Construct new URL
                header("Location: " . $newUrl); // Redirect to the new URL
                die(); // Stop further execution
            }
        }
    }
    public function searchPageAction()
    {
        $this->shopPageLogic(null); // Execute shop page logic
    }
    public function shopPageLogic($slug) {
        $obw = $this->obw;  
        $seo = $this->seo;
        $web_theme = isset($obw['web_theme']) ? $obw['web_theme'] : ""; // Get web theme
        $mobile_theme = isset($obw['mobile_theme']) ? $obw['mobile_theme'] : ""; // Get mobile theme
        $themeSettings = json_decode($obw['theme_settings'], true); // Decode theme settings 
        if ($themeSettings['theme_settings']['categories_top_nav_list_arrows_on_off'] == 1) {
            $slug = null;
        }
        $url = $this->constructURL($obw, $themeSettings, $slug); // Construct API URL
        $response = $this->getResponseFromURL($url); // Get API response
        $cart_feature_exsits = $obw['feature']; // Check if cart feature exists
        $cart_btn_status = $obw['status']; // Get cart button status
        $this->sendDataToView($response, $web_theme, $mobile_theme, $cart_feature_exsits, $cart_btn_status, $obw); // Send data to view
        $seo = $seo->getSeo($obw['brand_id'], "menu"); // Get SEO details
        $this->setBusinessDetail($obw, $seo, $themeSettings); // Set business details for view
        // Check if URL contains specific parameters for category SEO
        if (
            preg_match('/\bcat_id=[0-9]+\b/', $url) ||
            preg_match('/\bbrand_id=[0-9]+\b/', $url) ||
            preg_match('/\bprice_range=[0-9]+to[0-9]+\b/', $url)
        ) {
            $catSeo = $this->categorySeo($response); // Get category SEO
            $this->view->title = $catSeo['title'] ?? ''; // Set title
            $this->view->h1 = $catSeo['h1'] ?? ''; // Set h1
            $this->view->keywords = $catSeo['desc'] ?? ''; // Set keywords
            $this->view->desc = $catSeo['keywords'] ?? ''; // Set description
            $this->pickShopPageView($themeSettings , 'searchpage');
        } else {
            $this->pickShopPageView($themeSettings , 'shoppage');
        }    
    }
    private function pickShopPageView($themeSettings, $defaultView) {
        if ($themeSettings['theme_settings']['categories_top_nav_list_arrows_on_off'] == 1) {
            $this->view->pick('index/tabsshoppage'); // Pick tabbed shop page view
        } else {
            $this->view->pick('index/' . $defaultView); // Pick regular shop page view
        }
    }

    private function categorySeo($response){
        $data= [];
        foreach($response['items'] as $items){
            if(isset($items['category_seo']['title']) && $items['category_seo']['title'] != ''){
                $data['title'] = $items['category_seo']['title']; // Set title
              }
            
            if(isset($items['category_seo']['h1']) && $items['category_seo']['h1'] != ''){
                $data['h1'] = $items['category_seo']['h1']; // Set h1
             }
            
            if(isset($items['category_seo']['description']) && $items['category_seo']['description'] != ''){
                $data['desc'] = $items['category_seo']['description']; // Set description
             }
            
            if(isset($items['category_seo']['keywords']) && $items['category_seo']['keywords'] != ''){
                $data['keywords'] = $items['category_seo']['keywords']; // Set keywords
             } 
          }
        return $data;      
    }

    private function setBusinessDetail($obw,$seo,$themeSettings){
        $this->view->businessDetail = $obw; // Set business details
        $this->view->title = $seo['gtitle'] ?? ''; // Set title
        $this->view->h1 = $seo['gh1'] ?? ''; // Set h1
        $this->view->keywords = $seo['gky'] ?? ''; // Set keywords
        $this->view->desc = $seo['gdesc'] ?? ''; // Set description
        $this->view->themeSettings = $themeSettings; // Set theme settings
    }
    
    private function getResponseFromURL($url){
        if (!empty($url)) {
            // Use context options to handle errors properly
            $context = stream_context_create([
                'http' => [
                    'timeout' => 30, // 30 seconds timeout
                    'ignore_errors' => true // Don't throw errors, let us handle them
                ]
            ]);
            
            $instaResult = file_get_contents($url, false, $context); // Get content from URL

            // Check if the request failed
            if ($instaResult === false) {
                $this->redirectTo404();
                return [];
            }

            // Check if we got an empty response
            if (empty($instaResult)) {
                $this->redirectTo404();
                return [];
            }

            // Try to decode the JSON response
            $decodedResponse = json_decode($instaResult, true);

            // Check if JSON decoding failed
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->redirectTo404();
                return [];
            }

            // Check if the response contains an error or is empty
            if (empty($decodedResponse) || (isset($decodedResponse['error']) && $decodedResponse['error'])) {
                $this->redirectTo404();
                return [];
            }

            return $decodedResponse; // Return decoded JSON response
        } else {
            $this->redirectTo404();
            return []; // Return empty array if URL is empty
        }
    }

    private function redirectTo404() {
        // Set 404 header
        header("HTTP/1.0 404 Not Found");

        // Check if we have a custom 404 page
        $obw = $this->obw;
        $web_theme = isset($obw['web_theme']) ? $obw['web_theme'] : "";
        $mobile_theme = isset($obw['mobile_theme']) ? $obw['mobile_theme'] : "";

        // Set basic view data for 404 page
        $this->view->businessDetail = $obw;
        $this->view->web_theme = !empty($web_theme) ? json_decode($web_theme, true) : [];
        $this->view->mobile_theme = !empty($mobile_theme) ? json_decode($mobile_theme, true) : [];

        // Pick the 404 page view
        $this->view->pick('index/error');

        // Stop further execution
        return;
    }
    private function constructURL($obw , $themeSettings,$slug){
        // Determine the branch ID from the cookie, or use a default value
        $branch_id = (isset($_COOKIE['branch_id']) && $_COOKIE['branch_id'] != "") ?  $_COOKIE['branch_id'] : "18997";
        // Get the server name
        $name = $_SERVER['SERVER_NAME'];
         // Get query parameters from the request
        $categoryIdsParam = $this->request->getQuery('filter', 'string', '');
        $brandIdsParam = $this->request->getQuery('brand_id', 'string', '');
        $priceParam = $this->request->getQuery('price_range', 'string', '');
        $discountParam = $this->request->getQuery('special_items', 'string', '');
        // Initialize the URL string
        $url = "";
        // Check if $obw and its 'res_id' are set
        if(isset($obw) && isset($obw['res_id'])) {
            // Construct the base URL with business ID and branch ID
            //API_PATH is a constant 
            $url = API_PATH."products?business_id=" . $obw['res_id'] . "&attributes=1&branch_id=" . $branch_id . "&display_source=2&menu_type_id=0";
            // If a slug is provided, add it as a query parameter after decoding
            if(isset($slug) && $slug != ""){
                $url .= "&cat_id=" . urlencode($slug);
                 $url .= "&query=" . urlencode($slug);
                $url .= '&using_slug=true';
            }
            // Add additional filters to the URL if they are present in the request
            if (!empty($_GET['query']) || !empty($categoryIdsParam) || !empty($brandIdsParam) || !empty($priceParam) || !empty($discountParam)) {
                // Add a query parameter if it's provided in the request
                if (!empty($_GET['query'])) {
                    $queryParts = explode("-", $_GET['query']);
                    $url .= "&query=" . urlencode($queryParts[0]);
                }
                // Add category ID filter if it's provided
                if (!empty($categoryIdsParam)) {
                    $url .= '&cat_id=' . urlencode($categoryIdsParam);
                }
                // Add brand ID filter if it's provided
                if (!empty($brandIdsParam)) {
                    $url .= '&brand_id=' . urlencode($brandIdsParam);
                }
                // Add price range filter if it's provided
                if (!empty($priceParam)) {
                    $url .= '&price_range=' . urlencode($priceParam);
                }
                if(!empty($discountParam)){
                    $url .= '&discount=1';
                }
            }
            // Check if pagination is enabled in the theme settings, and if so, add pagination parameters to the URL
            if (isset($themeSettings['theme_settings']['menu_pagination_on_off']) && $themeSettings['theme_settings']['menu_pagination_on_off'] == 1) {
                $page = isset($_GET['page_no']) ? (int)$_GET['page_no'] : 1;
                $itemsPerPage = 100;
                $offset = ($page - 1) * $itemsPerPage;
                $url .= '&num=' . $itemsPerPage . '&offset=' . $offset;
            }
            // If the request is coming from a staging environment, modify the base URL
            if (preg_match("/^([^.]+)\.staging\.ordrz\.com/", $name)) {
                $url = "https://beta1.tossdown.com/api/products?business_id=" . $obw['res_id'] . "&branch_id=" . $branch_id;
            }
        }
        // Return the constructed URL
        echo $url;
        die();
        return $url;
    }
    private function sendDataToView($response, $web_theme, $mobile_theme, $cart_feature_exsits, $cart_btn_status,$obw)
    {
        $this->view->decimal_places = isset($obw['decimal_places']) ? $obw['decimal_places'] : 0;
        $this->view->theme_setting = json_decode($obw['theme_settings']);
        // Assign items from the response to the view, check if 'items' exists in the response
        $this->view->items = isset($response['items']) ? $response['items'] : [];
        // Assign categories from the response to the view, check if 'categories' exists in the response
        $this->view->categories = isset($response['categories']) ? $response['categories'] : [];
        // Assign brands from the response to the view, check if 'brands' exists in the response
        $this->view->brands = isset($response['brands']) ? $response['brands'] : [];
        // Decode and assign web theme JSON data to the view, check if $web_theme is not empty
        $this->view->web_theme = !empty($web_theme) ? json_decode($web_theme, true) : [];
        // Decode and assign mobile theme JSON data to the view, check if $mobile_theme is not empty
        $this->view->mobile_theme = !empty($mobile_theme) ? json_decode($mobile_theme, true) : [];
        // Assign the total count of items from the response to the view, check if 'items_count_all' exists in the response
        $this->view->items_count_all = isset($response['items_count']) ? $response['items_count'] : 0;
        // Assign cart feature existence status to the view
        $this->view->cart_feature_exsits = $cart_feature_exsits;
        // Assign cart button status to the view
        $this->view->cart_btn_status = $cart_btn_status;
        //sending logo to both pages search and sop page
        $this->view->logo = isset($obw['logo_image']) ? $obw['logo_image'] : 'no_image.jpg';

    }
    
}