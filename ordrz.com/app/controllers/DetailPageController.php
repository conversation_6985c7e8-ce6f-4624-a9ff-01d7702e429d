<?php
declare(strict_types=1);
// Import necessary namespaces
use Obw\Obw;
use Seo\Seo;

class DetailPageController extends ControllerBase
{
  // Declare public properties to hold instances of classes
  public $obw;
  public $seo;
  public $apiController;

  /**
   * Initialize function to set up objects and fetch business details.
   * This function is called automatically before any action is executed.
   */
  public function initialize()
  {
    // Initialize an instance of the Obw class
    $this->obw = new Obw();

    // Initialize an instance of the Seo class
    $this->seo = new Seo();

    // Initialize an instance of the ApiController class
    $this->apiController = new ApiController();

    // Get the server name (the current domain name)
    $name = $_SERVER['SERVER_NAME'];

    // Fetch the business username using the domain name
    $businessUsername = $this->obw->domainDetails($name);

    // Get full business details using the business username and store it in the $obw property
    $this->obw = $this->obw->getBusinessDetail($businessUsername);
  }

  /**
   * Detail page action that handles the product detail page rendering.
   * 
   * @param string $item_name The name of the item, typically including the item ID.
   */
  public function detailPageAction($item_name)
  {
    // Extract the item ID from the item name
    $id = $this->extractItemId($item_name);

    // Store the business detail in the view object for access in the view files
    $this->view->businessDetail = $this->obw;
    $theme_Settings = json_decode($this->obw['theme_settings']);
    $theme_Settings = $theme_Settings->theme_settings;

    // Fetch product details using the business ID and item ID
    $data = $this->fetchProductDetails($this->obw['res_id'], $id);

    // Extract SEO data from the fetched product details if available, or use an empty array as default
    $seo = $data['items'][0]['seo'] ?? [];

    // Set various view data, such as title, description, keywords, etc.
    $this->setViewData($data, $seo, $this->obw);
    // Pick the 'index/detailpage' view template to render the detail page
    if (empty($data['items'])) {
      $this->view->pick('index/error');
      return ;
    }
    if ($theme_Settings->new_detail_page_on_off == 1 && !empty($this->obw['web_theme'])) {
      $this->new_detail_page($data['items'][0], $this->obw , $theme_Settings);
    } else {
      $this->view->pick('index/detailpage');
    }
  }
  public function boxAction($item_id){
   
    $this->view->businessDetail = $this->obw;
    $data = $this->fetchProductDetails($this->obw['res_id'], $item_id);
    $seo = $data['items'][0]['seo'] ?? [];
   
    $this->setViewData($data, $seo, $this->obw);
    $this->view->pick('theme/box');
  }
  /**
   * Extract the item ID from the item name.
   * The item name typically contains an ID at the end, separated by hyphens.
   *
   * @param string $item_name The item name containing the ID.
   * @return string The extracted item ID.
   */
  private function extractItemId($item_name)
  {
    // Split the item name by hyphen and return the last part, which is assumed to be the item ID
    $item = explode('-', $item_name);
    return end($item);
  }

  /**
   * Fetch product details from an external API using business ID and item ID.
   *
   * @param string $business_id The ID of the business (restaurant) to fetch details for.
   * @param string $item_id The ID of the item (product) to fetch details for.
   * @return array The product details as an associative array.
   * @throws Exception If the HTTP request fails or the JSON response cannot be decoded.
   */
  private function fetchProductDetails($business_id, $item_id)
  {
    // Construct the API URL to fetch the product details
    $url = API_PATH . "product_details?business_id=" . $business_id . "&item_id=" . $item_id;

    // Make an HTTP GET request to the API
    $response = file_get_contents($url);
    
    // Throw an exception if the request failed
    if ($response === false) {
      throw new Exception("Failed to fetch product details. Error: HTTP request failed.");
    }

    // Decode the JSON response into an associative array
    $data = json_decode($response, true);

    // Throw an exception if the JSON response could not be decoded
    if (json_last_error() !== JSON_ERROR_NONE) {
      throw new Exception("Failed to decode JSON response.");
    }

    return $data; // Return the decoded product details
  }

  /**
   * Extract and replace specific tags within a product description, such as <short_desc> and <long_desc>.
   *
   * @param string $productDesc The product description containing HTML tags.
   * @return string The extracted and sanitized description.
   */
  public function replaceTags($productDesc)
  {
    // Check if the <short_desc> tag exists in the product description
    if (preg_match('/<short_desc>(.*?)<\/short_desc>/s', $productDesc, $matches)) {
      $shortDesc = strip_tags($matches[1]); // Extract and strip HTML tags from the content
    }

    // Check if the <long_desc> tag exists in the product description
    if (preg_match('/<long_desc>(.*?)<\/long_desc>/s', $productDesc, $matches)) {
      $shortDesc = strip_tags($matches[1]); // Extract and strip HTML tags from the content
    }

    return $shortDesc; // Return the sanitized description
  }

  /**
   * Set the necessary view data for rendering the product detail page.
   *
   * @param array $data The product data fetched from the API.
   * @param array $seo The SEO data for the product.
   * @param array $obw The business (restaurant) details.
   */
  private function setViewData($data, $seo, $obw)
  {
    $this->view->decimal_places = isset($obw['decimal_places']) ? $obw['decimal_places'] : 0;
    // Assign the product data to the view
    $this->view->data = $data['items'][0];

    // Set the page title using the SEO title or fallback to the product name and category
    $this->view->title = $seo['title'] ?? $data['items'][0]['name'] . " - " . $data['items'][0]['category'];

    // Set the H1 header using the SEO H1 or fallback to the product name and category
    $this->view->h1 = $seo['h1'] ?? $data['items'][0]['name'] . " - " . $data['items'][0]['category'];

    // Set the meta description using the SEO description or fallback to the sanitized product description
    $this->view->desc = $seo['description'] && !empty($data['items'][0]['desc']) ?? $this->replaceTags($data['items'][0]['desc']);

    // Set the meta keywords using the SEO keywords or fallback to the product name and category
    $this->view->keywords = $seo['keywords'] ?? $data['items'][0]['name'] . " - " . $data['items'][0]['category'];

    // Assign the API response data to the view
    $this->view->response = $data;

    // Assign the business details to the view for rendering
    $this->view->businessDetail = $obw;
    $this->view->resturent_details = $obw;

    // Set the cart feature status and button status in the view
    $this->view->cart_feature_exsits = $obw['feature'];
    $this->view->cart_btn_status = $obw['status'];

    // If a web theme exists in the business details, decode and assign it to the view
    if (isset($obw["web_theme"]) && $obw['web_theme'] != "") {
      $this->view->web_theme = json_decode($obw['web_theme'], true);
    }

    // If a mobile theme exists in the business details, decode and assign it to the view
    if (isset($obw["mobile_theme"]) && $obw['mobile_theme'] != "") {
      $this->view->mobile_theme = json_decode($obw['mobile_theme'], true);
    }

    // Assign the web theme to a 'theme' variable in the view for generic access
    if (isset($obw["web_theme"]) && $obw['web_theme'] != "") {
      $this->view->theme = json_decode($obw['web_theme'], true);
    }
  }




  public function new_detail_page($detail_item_data, $obw , $theme_Settings)
  {

    $webTheme = json_decode($obw['web_theme'], true);

    // Extract the 'detail_page_editor' from 'web_theme', set to null if not available or invalid JSON
    $detailPageEditor = (json_last_error() === JSON_ERROR_NONE && isset($webTheme['detail_page']['detail_page_editor']))
      ? $webTheme['detail_page']['detail_page_editor']
      : null;

    // Initialize an empty data array and set the out-of-stock message
    $detailPageContent = [];
    $out_of_stock = '';

    // Check the product's availability status and set appropriate messages
    if (!empty($detail_item_data['status'])) {
      switch ($detail_item_data['status']) {
        case 1:
          // Product is out of stock
          $out_of_stock = $theme_Settings->product_availability_text;
          break;
        case 2:
          // Product not available
          $out_of_stock = 'Item currently not available';
          break;
        default:
          // No message for available product
          $out_of_stock = '';
      }
    }
    $branch_id = isset($_COOKIE['branch_id']) ? $_COOKIE['branch_id'] : '';

    // Create the array for product details by mapping detail data with placeholders in the template
    $detailPageContent['product_details_values'] = [
      "{{name}}" => $detail_item_data['name'],
      "{{brand}}" => $detail_item_data['item_brand'][0]['brand_name'] ?? '',
      "{{category}}" => $detail_item_data['category'] ?? '',
      "{{weight}}" => $detail_item_data['weight'] ?? '',
      "{{weight_unit}}" => $detail_item_data['weight_unit'] ?? '',
      "{{sku}}" => $detail_item_data['sku'] ?? '',
      "{{price}}" => $this->get_price( $detail_item_data),
      "{{currency}}" => getCurrencySymbol($detail_item_data['currency']) ?? '',
      "{{price_per}}" => $detail_item_data['price_per'] ?? '',
      "{{out_of_stock}}" => $out_of_stock,
      "{{discount_price}}" => $this->get_discount_price( $detail_item_data),
      "{{discount_percentage}}" => $this->get_discount_percentage( $detail_item_data),
      "{{tax_percentage}}" => $this->get_tax_percentage( $detail_item_data),
      "{{counter}}" => $this->get_counter($theme_Settings, $detail_item_data , $branch_id,$obw),
      "{{note}}" => $detail_item_data['note'] ?? '',
      "{{special_instructions}}" => (!empty($detail_item_data['allow_note']) && $detail_item_data['allow_note'] == 1)
        ? '<div class="form-group" style="padding-left: 0px !important;">
                  <label class="popup-label" style="margin-top:.5rem;font-weight:bold;">Special Instructions</label>
                  <textarea  style="margin-right: 5px;margin-top: 5px;"
                      id="special-'.$detail_item_data['menu_item_id'].'"
                      data-name=""
                      class="form-control comment my-3"
                      type="textbox"
                      name="comment"></textarea>
                </div>'
        : '',
      "{{desc}}" => $detail_item_data['desc'] ?? '',
      "{{short_desc}}" => $this->get_short_desc($detail_item_data['desc'] ?? ''),
      "{{long_desc}}" => $this->get_long_desc($detail_item_data['desc'] ?? ''),
      "{{option_set}}" => $this->get_option_set($theme_Settings, $detail_item_data),
      "{{share_button}}" => $this->get_share_button(),
      "{{cart_button_start}}" => $this->get_cart_button(  $detail_item_data , $theme_Settings ,$branch_id, $obw),
      "{{cart_button_end}}" => isset($deatil_item_data['status']) && $detail_item_data['status'] == 0 ? '</a>' : '',
      "{{grouped_items}}" => isset($theme_Settings->group_items_setting_on_off) && $theme_Settings->group_items_setting_on_off == 1 ? $this->get_grouped_items_by_attribute($theme_Settings,$theme_Settings->group_items_by_name, $detail_item_data) : $this->get_grouped_items($theme_Settings,$detail_item_data) ,

    ];

  // Replace placeholders in the 'detail_page_editor' template with actual product details
    $detailPageContent['detail_page_html'] = strtr($detailPageEditor, $detailPageContent['product_details_values']);
    // Assign data to the view variables for rendering
    $shopPageUrl = isset($theme_Settings->shop_page_on_off) && $theme_Settings->shop_page_on_off == 0  ? 'menu' : 'shop';
    $this->view->breadcrumbs = $this->renderBreadcrumbs($detail_item_data , $shopPageUrl);
    $this->view->decimal_places = isset($obw['decimal_places']) ? $obw['decimal_places'] : 0;
    $this->view->detailPageContent = $detailPageContent;
    $this->view->detail_item_data = $detail_item_data;
    $this->view->detailPageFromObTool = $detailPageEditor;
    $this->view->theme_setting = json_decode($obw['theme_settings']);

    // Render the detailed page view
    $this->view->pick('index/newdetailpage');
  }
  public function get_grouped_items_by_attribute($data, $attribute_name, $deatil_data){
    if(isset($deatil_data['grouped_products']) && !empty($deatil_data['grouped_products']) &&  $deatil_data['grouped_products'] != NULL) {
      $html.="<div class='group-items-detail' style='margin: 10px 0px 10px 0px;'>";
        $html.="<ul>";
        foreach($deatil_data['grouped_products'] as $group_item) {
          $regex = '#[^a-z0-9_]#i';
          $pname = preg_replace($regex, '-', $group_item['name']);
          $pro_name = strtolower($pname);

          $color = array_column($group_item['attributes'], $attribute_name);
          $color_res = isset($color[0]) ? $color[0] : '';
          if($group_item['product_id'] == $data['detail']['items'][0]['menu_item_id']){
            $border_style = "border: 3px solid #0097D6;";
          } else {
            $border_style = "border: 1px solid #0097D6;";
          }
         if($color_res != ''){
            $html.="<li style='display: inline-block;'>
                        <a data-toggle='tooltip' data-placement='top' title='".$color_res."' style='margin-right: 7px; ".$border_style." display: inline-block; border-radius: 5px; width: 48px; height: 50px; text-align: center; padding: 3px;'>
                            <img src='".$group_item['image']."' style=' text-align: center; width: 100%; height: 100%;'>
                            </br>
                            <p class='detail_page_attribute'>".$color_res."</p>
                        </a>
                    </li>";
          }
        }
        $html.="</ul>";
      $html.="</div>";
    }
    return $html;
  }
  public function get_grouped_items($data, $deatil_data){
    if(isset($deatil_data['grouped_products']) && !empty($deatil_data['grouped_products']) &&  $deatil_data['grouped_products'] != NULL) {
      $html.="<div class='group-items-detail' style='margin: 10px 0px 10px 0px;'>";
        $html.="<ul>";
        foreach($deatil_data['grouped_products'] as $group_item) {
          $regex = '#[^a-z0-9_]#i';
          $pname = preg_replace($regex, '-', $group_item['name']);
          $pro_name = strtolower($pname);
          $color = array_column($group_item['attributes'], 'Color');
          $color_res = isset($color[0]) ? $color[0] : '';
          if($group_item['product_id'] == $data['detail']['items'][0]['menu_item_id']){
            $border_style = "border: 3px solid #0097D6;";
          } else {
            $border_style = "border: 1px solid #0097D6;";
          }
            $html.="<li style='display: inline-block;'>
            <a data-toggle='tooltip' data-placement='top' title='".$color_res."'
            style='margin-right: 7px; ".$border_style." display: inline-block; border-radius: 5px; width: 48px; height: 50px; text-align: center; padding: 3px;'>
                <img src='".$group_item['image']."' style=' text-align: center; width: 100%; height: 100%;'>
            </a>
        </li>";
        }
        $html.="</ul>";
      $html.="</div>";
    }
    return $html;
  }
  public function get_cart_button($deatil_item_data , $theme_Settings , $branch_id,$obw) {
    $url = '';
$class = '';
$target = '';
$regex = '#[^a-z0-9_]#i';
$pname = preg_replace($regex, '-', isset($deatil_item_data['name']) ? $deatil_item_data['name'] : '');
$product_name = strtolower($pname);
$data = json_decode(isset($obw['web_theme']) ? $obw['web_theme'] : '{}', true);
$product_code = isset($data['card']['search_results']['settings']['product_code']) ? $data['card']['search_results']['settings']['product_code'] : 0;
$domain = isset($data['card']['search_results']['settings']['domain']) ? $data['card']['search_results']['settings']['domain'] : '';

if ($product_code == 1) {
    $url = $domain . "/product/" . $product_name . "-" . (isset($deatil_item_data['product_code']) ? $deatil_item_data['product_code'] : '');
    $class = '';
    $target = "target='_blank'";
} else {
    $url = 'javascript:void(0);';
    $class = 'detail_add_cart_btn';
    $target = '';
}
$html = "";
if ($branch_id == '' || (isset($deatil_item_data['branch_na']) && is_array($deatil_item_data['branch_na']) && !in_array($branch_id, $deatil_item_data['branch_na']))) {
    if (isset($deatil_item_data['status']) && $deatil_item_data['status'] == 0 && isset($obw['status']) && $obw['status'] != 'hide') {
      $html.="<a href='" . $url . "' $target class='" . $class . " detail-page-add-btn detail-add-to-cart cursor-pointer'
              data-id='".(isset($deatil_item_data["menu_item_id"]) ? $deatil_item_data["menu_item_id"] : '') . "'> 
              <span class='cartBtn'>".(isset($theme_Settings->cart_button_text) ? $theme_Settings->cart_button_text : '') . "</span>
              <div class='spinner-border loader_popup hide' role='status'>
                    <span class='sr-only'>Loading...</span>
               </div>
            </a>";
    }
  }else{
    $html .="<span class='item-unavailable-message'>Oops! This item isn’t available on this branch.</span>";
  }
  return $html;
}
  public function get_share_button()
  {
    // Initialize HTML for the share buttons
    $html = '';

    // Generate the current page URL
    $actual_link = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];

    // Add social media share buttons with respective URLs
    $html .= "
          <label style='margin-top: 20px;font-weight: bold;'>Share via</label>
          <div class='share-button' style='width: 100%;display: flex;padding: 2px;'>
              <a target='_blank' href='https://www.facebook.com/sharer/sharer.php?u=" . $actual_link . "' class='btn btn-primary facebook' style='background: #425893;'>
              <i class='fab fa-facebook-f'></i></a>
              <a target='_blank' href='https://l.instagram.com/?u=" . $actual_link . "' class='btn btn-primary instagram' style='background: #e95950;'>
              <i class='fab fa-instagram'></i></a>
              <a target='_blank' href='https://twitter.com/intent/tweet?text=my share text&amp;url=" . $actual_link . "' class='btn btn-primary twitter' style='background: #4D9EEA;'>
              <i class='fab fa-twitter'></i></a>
              <a target='_blank' href='https://wa.me/?text=" . $actual_link . "' class='btn btn-primary whatsapp' style='background: #65D072;'>
              <i class='fab fa-whatsapp'></i></a>
          </div>";

    return $html; // Return the HTML for share buttons
  }

  public function get_long_desc($desc)
  {
    // Extract and return the long description from the input description string
    if (preg_match('/<long_desc>(.*?)<\/long_desc>/', $desc, $long_desc)) {
      return $long_desc[1];
    }
    return ''; // Return empty string if no long description found
  }

  public function get_price($detail_item_data)
  {
    $html = '';
    if (!empty($detail_item_data['discount_value']) && $detail_item_data['discount_value'] > 0) {
    $price = $detail_item_data['price'] ?? '';

    // Check if the product has a price
    if (!empty($price)) {
      $discount_class = '';

      // Check if the product has a discount and apply a discount class
        $discount_class = 'text_decoration_line_through';
        // Generate HTML for price with currency and discount
        $html .= "<strong data-currency='" . $detail_item_data["currency"] . "' data-price='" . $price . "' id='orignal_price_menu_item_id_" . $detail_item_data["menu_item_id"] . "' class='font_weight_900 font_size_20 obw-primary-color " . $discount_class . "'>" . getCurrencySymbol($detail_item_data["currency"]) . " " . formatPrice($price) . "</strong>";
      } else {
        // Handle cases where the price is defined by options
        $from_price = $detail_item_data["options"][0]['items'][0]['price'] ?? '';
        if (!empty($from_price)) {
          $html .= "<strong data-currency='" . $detail_item_data["currency"] . "' data-price='" . $price . "' id='orignal_price_menu_item_id_" . $detail_item_data["menu_item_id"] . "' class='font_weight_900 font_size_20 obw-primary-color '>From " . getCurrencySymbol($detail_item_data["currency"]) . " " . formatPrice($from_price) . "</strong>";
        }
      }
    }
    return $html; // Return the generated price HTML
  }

  public function get_discount_price($detail_item_data)
  {
    $html = '';
    // Check if a discount is applied
    if (isset($detail_item_data['price'])) {
      // Calculate the discounted price
      $discounted_price =$detail_item_data['price'] - $detail_item_data['discount_value'];
      $currency = getCurrencySymbol($detail_item_data['currency']);
      // Generate the HTML for the discounted price
      $price = $detail_item_data['price'] > 0 ? formatPrice($discounted_price) : '';
      $display_price = $price ? "<strong class='currency'>{$currency}</strong> <strong class='amount'>{$price}</strong>" : '';
      $html .= "<strong class='detail_discount_price product_detail_prices detail_prices'>{$display_price}</strong>";
      $html .= "<input type='hidden' id='finalDetailPagePrice' value='{$discounted_price}' data-currency='{$currency}'>";

}
    return $html; // Return the generated discount price HTML
  }

  public function get_discount_percentage($detail_item_data)
  {
    // Calculate and return the discount percentage
    if (!empty($detail_item_data['discount']) && $detail_item_data['discount'] > 0) {
      return $detail_item_data['discount'] . "% OFF";
    }
    return ''; // Return empty string if no discount is available
  }

  public function get_tax_percentage($detail_item_data)
  {
    $html = '';
    // Check if tax is applied and generate the tax HTML
    if (!empty($detail_item_data['tax'])) {
      $html .= "<span class='detail_tax'>(Tax: " . $detail_item_data['tax'] . "%" . ")</span>";
    }
    return $html; // Return the tax percentage HTML
  }

  public function get_counter($theme_Settings, $detail_item_data , $branch_id,$obw)
  {
    if ($branch_id == '' || (isset($detail_item_data['branch_na']) && !in_array($branch_id, $detail_item_data['branch_na']))) {
      if($detail_item_data['status'] == 0 && $obw['status'] != 'hide'){
            $html.="<div class='counter_btns_det' style='display: -webkit-box; display: -ms-flexbox; display: flex; float: left; -ms-flex-wrap: wrap; flex-wrap: wrap; padding-top: 6px; padding-bottom: 2px; padding-right: 20px;'>";
            $html.="<div class='counter_minus_btn'>";
            $html.="<a style='font-size: 20px; padding: 0px 6px; color: #".$theme_Settings->secondary_font_color." ; border-radius: 3px; display: inline-block; width: 25px; height: 25px; font-weight: bolder;'
                      href='javascript:;'>";
            $html.="<i class='fa fa-minus decrement_btn'></i>";
            $html.="</a>";
            $html.="</div>";
            $html.="<div class='counter_qty counter_qty_det'>";
            $varriable = $detail_item_data["min_qty"] > 1 ? $detail_item_data["min_qty"] : 1;
            $html .= "<span class='detail-page-qty total_qty' id='" . $detail_item_data['menu_item_id'] . "_qty'>" . $varriable . "</span>";
            $html.="</div>";
            $html.="<div class='counter_plus_btn'>";
            $html.="<a style='font-size: 20px;padding: 0px 7px;color: #".$theme_Settings->secondary_font_color."; border-radius: 3px;display: inline-block;width: 25px;height: 25px;font-weight: bolder;'
                      href='javascript:;'>";
                      $html.="<i class='fa fa-plus increment_btn'></i>";
            $html.="</a>";
            $html.="</div>";
            $html.="</div>";
        }
      }
      return $html;
  }

  public function get_short_desc($desc)
  {
    // Extract and return the short description from the input description string
    if (preg_match('/<short_desc>(.*?)<\/short_desc>/', $desc, $short_desc)) {
      return $short_desc[1];
    }
    return ''; // Return empty string if no short description found
  }

 
  public function get_option_set($data, $detail_item_data)
  {
    $resultHtml = ""; // Initialize an empty string to hold the result
    $currency = getCurrencySymbol($detail_item_data['currency']);
    foreach ($detail_item_data['options'] as $index => $element) {
      $resultHtml .= $this->renderAccordionElement($element, $index, $currency); // Append the result of each iteration
    }
    return $resultHtml;
  }

  public function renderAccordionElement($element, $index, $currency)
  {
    if (isset($element['items']) && is_array($element['items'])) {
      $inputType = ($element['min_quantity'] == 1 && $element['quantity'] == 1) ? "radio" : (($element['min_quantity'] == 0 && $element['quantity'] > 1) ? "number" : "checkbox");
      $isOptional = ($element['quantity'] == 0) ? "Optional" : "Required";
      $outerOptionsetId = $element['id'];
      $innerOptionCount = array_reduce($element['items'], function ($count, $option) {
        return $count + (isset($option['items']) ? count($option['items']) : 0);
      }, 0);

      $optionsHtml = $this->renderOptionsHtml($element, $inputType, $currency);

      if ($inputType === "number") {
        return $this->renderNumberOptionSet($element, $index, $optionsHtml, $isOptional, $innerOptionCount, $outerOptionsetId);
      } else {
        return $this->renderOtherOptionSet($element, $index, $optionsHtml, $isOptional, $innerOptionCount, $outerOptionsetId);
      }
    } else {
      $isOptional = ($element['quantity'] === 0) ? "Optional" : "Required";
      return $this->renderNoOptionsAvailable($element, $index, $isOptional);
    }
  }

  public function renderNumberOptionSet($element, $index, $optionsHtml, $isOptional, $innerOptionCount, $outerOptionsetId)
  {
    return '
    <div class="single_optionset d-flex flex-column">
        <h2 class="optionset_header d-flex flex-column" id="optionsetheading' . $index . '">
            <button class="optionset_header_btn accordion-button d-flex align-items-center justify-content-between"
                type="button" data-bs-toggle="collapse" data-bs-target="#optionsetCollapse' . $index . '"
                aria-expanded="false" aria-controls="optionsetCollapse' . $index . '">
                <div class="optionset_header_data d-flex flex-column">
                    <span>' . $element['name'] . '</span>
                    <div class="d-flex align-items-center">
                    <small class="small_validation" name="' . $element['name'] . '" id="' . $element['id'] . '">' . $isOptional . '&nbsp</small>
                    <label name="' . $element['name'] . '" id="' . $element['id'] . '" class="lable_validation error_validation_' . $element['menu_item_id'] . '"></label>
                    </div>
                </div>
                <i class="fas fa-chevron-down ms-auto"></i>
            </button>
        </h2>
        <div id="optionsetCollapse' . $index . '" class="accordion-collapse collapse show"
            aria-labelledby="optionsetheading' . $index . '" data-bs-parent="#optionsetAccordion">
            <ul class="optionset_listing d-flex">
                ' . implode("", $optionsHtml) . '
            </ul>
            ' . ($innerOptionCount > 0 ? '
            <div class="inner_options_container d-flex flex-column inner_option_parent" id="innerOptionsContainer_' . $outerOptionsetId . '">
                <div class="inner_optionset_listing d-flex" id="inner_option_input"></div>
            </div>' : "") . '
        </div>
    </div>';
  }

  public function renderOtherOptionSet($element, $index, $optionsHtml, $isOptional, $innerOptionCount, $outerOptionsetId)
  {
    return '
    <div class="single_optionset d-flex flex-column">
        <h2 class="optionset_header d-flex flex-column" id="optionsetheading' . $index . '">
            <button class="optionset_header_btn accordion-button d-flex align-items-center justify-content-between"
                type="button" data-bs-toggle="collapse" data-bs-target="#optionsetCollapse' . $index . '"
                aria-expanded="false" aria-controls="optionsetCollapse' . $index . '">
                <div class="optionset_header_data d-flex flex-column">
                    <span>' . $element['name'] . '</span>
                    <div class="d-flex align-items-center">
                    <small class="small_validation" name="' . $element['name'] . '" id="' . $element['id'] . '">' . $isOptional . '&nbsp</small>
                    <label name="' . $element['name'] . '" id="' . $element['id'] . '" class="lable_validation"></label>
                    </div>
                </div>
                <i class="fas fa-chevron-down ms-auto"></i>
            </button>
        </h2>
        <div id="optionsetCollapse' . $index . '" class="accordion-collapse collapse show"
            aria-labelledby="optionsetheading' . $index . '" data-bs-parent="#optionsetAccordion">
            <ul class="optionset_listing d-flex">
                ' . implode("", $optionsHtml) . '
            </ul>
            ' . ($innerOptionCount > 0 ? '
            <div class="inner_options_container d-flex flex-column inner_option_parent" id="innerOptionsContainer_' . $outerOptionsetId . '">
                <div class="inner_optionset_listing d-flex" id="inner_option_input"></div>
            </div>' : "") . '
        </div>
    </div>';
  }

  public function renderNoOptionsAvailable($element, $index, $isOptional)
  {
    return '
    <div class="single_optionset d-flex flex-column">
        <h2 class="optionset_header" id="optionsetheading' . $index . '">
            <button class="optionset_header_btn accordion-button d-flex align-items-center justify-content-between"
                type="button" data-bs-toggle="collapse" data-bs-target="#optionsetCollapse' . $index . '"
                aria-expanded="false" aria-controls="optionsetCollapse' . $index . '">
                <div class="optionset_header_data">
                    <span>' . $element['name'] . '</span>
                    <small>(' . $isOptional . ')</small>
                </div>
                <i class="fas fa-chevron-down ms
                <i class="fas fa-chevron-down ms-auto"></i>
            </button>
        </h2>
        <div id="optionsetCollapse' . $index . '" class="accordion-collapse collapse show"
            aria-labelledby="optionsetheading' . $index . '" data-bs-parent="#optionsetAccordion">
            <p>No options available</p>
        </div>
    </div>';
  }

  public function renderOptionsHtml($element, $inputType, $currency)
  {
    return array_map(function ($option) use ($element, $inputType , $currency) {
      $imageHtml = (isset($option['image']) && $option['image'] != "https://static.tossdown.com/images/") ? '<figure class="w-100"><img class="w-100 h-100 object-fit-contain" src="' . $option['image'] . '" /></figure>' : "";
      $priceHtml = (isset($option['price']) && $option['price'] != 0 && $option['price'] != 0.0 && $option['price'] != "0" && $option['price'] != "0.00") ?  $currency .' '.formatPrice($option['price']) : "";
      if ($inputType === "number") {
        return '
            <li>
                <div class="option_set_values counter_parent">
                    ' . $imageHtml . '
                    <small>' . $option['name'] . '</small>
                    <span>' . $priceHtml . '</span>
                    <div class="counter product_cart_btn">
                        <a class="counter-btn minus qty_increased">-</a>
                        <input data-qty-min="' . (isset($element['min_quantity']) && $element['min_quantity'] != "" ? $element['min_quantity'] : 0) . '" type="checkbox" data-qty="' . $element['quantity'] . '" class="option_set_validation detail-page-options" name="' . $element['name'] . '" data-id="' . $option['id'] . '" value="' . $option['name'] . ',' . $option['price'] . '" data-qty-current="0">
                        <span class="counter-input">0</span>
                        <a class="counter-btn plus qty_increased">+</a>
                    </div>
                </div>
            </li>';
      } else {
        return '
            <li>
                <label class="option_set_values">
                    <input data-qty-min="' . $element['min_quantity'] . '" type="' . $inputType . '" data-qty="' . $element['quantity'] . '" class="option_set_validation detail-page-options" name="' . $element['name'] . '" data-id="' . $option['id'] . '" value="' . $option['name'] . ',' . $option['price'] . '">
                    ' . $imageHtml . '
                    <small>' . $option['name'] . '</small>
                    <span>' . $priceHtml . '</span>
                </label>
            </li>';
      }
    }, $element['items']);
  }
  public function renderBreadcrumbs($detail_item_data , $shopPageUrl) {
    if (isset($detail_item_data)) {
      if(isset($detail_item_data['menu_cat_slug']) && $detail_item_data['menu_cat_slug'] != ""){
        $slug = $detail_item_data['menu_cat_slug'];
      }else{
        $slug = strtolower(trim(preg_replace('/[\s-]+/', '-', preg_replace('/[^A-Za-z0-9-]+/','-', preg_replace('/[&]/', '-', preg_replace('/[\']/', '-', iconv('UTF-8', 'ASCII//TRANSLIT', $detail_item_data['category']))))), '-'));
      }
      $html = "";
      $html = '<ul class="breadcrumbs">
      <li><a href="' . htmlspecialchars("https://" . $_SERVER['SERVER_NAME']) . '">Home</a></li>
      <li><a href="' . htmlspecialchars("https://" . $_SERVER['SERVER_NAME'] . '/' . $shopPageUrl) . '">' . ucfirst($shopPageUrl) . '</a></li>
      <li><a href="' . htmlspecialchars("https://" . $_SERVER['SERVER_NAME'] . '/' . $shopPageUrl .'/' . $slug) . '">' . htmlspecialchars($detail_item_data['category']) . '</a></li>
      <li><a href="' . htmlspecialchars("https://" . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI']) . '">' . htmlspecialchars($detail_item_data['name']) . '</a></li>
      </ul>';
      return $html;
    }
}
}