<?php

$router = $di->getRouter();

// Define your routes here

$router->handle($_SERVER['REQUEST_URI']);
$router->add(
    '/{slug:[a-zA-Z0-9\-]+}',
    [
        'controller' => 'index',
        'action'     => 'staticPage',
    ]
);
$router->add(
    '/{id:\d+}',
    [
        'controller' => 'index',
        'action'     => 'index',
    ]
);

$router->add(
    '/order/payment-confirmation/{gateway}',
    [
        'controller' => 'index',
        'action'     => 'paymentConfirmation',
    ]
);
$router->add(
    '/product/{slug:[a-zA-Z0-9\-]+}',
    [
        'controller' => 'DetailPage',
        'action'     => 'detailPage',
    ]
);

$router->add(
    '/shop/{slug:[a-zA-Z0-9\-]+}',
    [
        'controller' => 'ShopPage',
        'action'     => 'ShopFilters',
    ]
);
$router->add(
    '/menu/{slug:[a-zA-Z0-9\-]+}',
    [
        'controller' => 'ShopPage',
        'action'     => 'MenuFilters',
    ]
);
$router->add(
    '/shop',
    [
        'controller' => 'ShopPage',
        'action'     => 'shopPage',
    ]
);
$router->add(
    '/search',
    [
        'controller' => 'ShopPage',
        'action'     => 'searchPage',
    ]
);
$router->add(
    '/menu',
    [
        'controller' => 'ShopPage',
        'action'     => 'menuPage',
    ]
);
$router->add(
    '/brands',
    [
        'controller' => 'Brand',
        'action'     => 'index',
    ]
);
$router->add(
    '/location',
    [
        'controller' => 'index',
        'action'     => 'locations',
    ]
);
$router->add(
    '/profile',
    [
        'controller' => 'profile',
        'action'     => 'index',
    ]
);


$router->add(
    '/profile/{slug:[a-zA-Z0-9\-]*}',
    [
        'controller' => 'profile',
        'action'     => 'index',
    ]
);

$router->add(
    '/checkout',
    [
        'controller' => 'checkout',
        'action'     => 'index',
    ]
);
$router->add(
    '/new_contact_us',
    [
        'controller' => 'Website',
        'action'     => 'newContactUs',
    ]
);
$router->add(
    '/reserve/table',
    [
        'controller' => 'website',
        'action'     => 'reservetable',
    ]
);
$router->add(
    '/website/placeorder/{id:\d+}',
    [
        'controller' => 'website',
        'action'     => 'bankAlfalah',
    ]
);
$router->add(
    '/make-a-box/{slug:[a-zA-Z0-9\-]+}',
    [
        'controller' => 'DetailPage',
        'action'     => 'box',
    ]
);
$router->add(
    '/account',
    [
        'controller' => 'website',
        'action'     => 'account',
    ]
);

$router->add(
    '/signup/forget_password',
    [
        'controller' => 'signup',
        'action'     => 'forgetpassword',
    ]
);
$router->add(
    '/signup/confirm_password',
    [
        'controller' => 'signup',
        'action'     => 'confirmPassword',
    ]
);
$router->add(
    '/signup/change_password/{token}/{userId}',
    [
        'controller' => 'signup',
        'action'     => 'changePassword',
    ]
);

$router->add(
    '/website/insert_signup_newsletter',
    [
        'controller' => 'website',
        'action'     => 'newsletter',
    ]
);


$router->add(
    '/robots.txt',
    [
        'controller' => 'website',
        'action'     => 'robots',
    ]
);

$router->add(
    '/sitemap',
    [
        'controller' => 'website',
        'action'     => 'sitemap',
    ]
);