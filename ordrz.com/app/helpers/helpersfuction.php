<?php

function uploadToS3($tmp_name, $file_type, $file_name, $img_type = "site")
{
    $url = S3_IMAGE_UPLOAD_URL;
    $postField = array();
    $postField['files'] =  curl_file_create($tmp_name, $file_type, $file_name);
    $postField['im_type'] = 'web';
    $postField['image_type'] = $img_type;
    $postField['resize'] = '1';

    $headers = array("Content-Type" => "multipart/form-data");
    $curl_handle = curl_init();
    curl_setopt($curl_handle, CURLOPT_URL, $url);

    curl_setopt($curl_handle, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl_handle, CURLOPT_POST, TRUE);
    curl_setopt($curl_handle, CURLOPT_POSTFIELDS, $postField);
    curl_setopt($curl_handle, CURLOPT_RETURNTRANSFER, TRUE);
    $returned_fileName = curl_exec($curl_handle);
    curl_close($curl_handle);

    $result = json_decode($returned_fileName);
    return $result[0]->imageUrl;
}

function uploadFiles($file)
{
    $url = "https://fimxoantf4.execute-api.us-east-1.amazonaws.com/dev/assets/upload";

    // Ensure file was uploaded without error
    if (!isset($file['tmp_name']) || $file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }

    // Create CURL file object
    $postFields = [
        'file' => curl_file_create(
            $file['tmp_name'],
            mime_content_type($file['tmp_name']),
            $file['name']
        )
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    // Optional: set headers if needed
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Content-Type: multipart/form-data"
    ]);

    $response = curl_exec($ch);
    curl_close($ch);

    // Decode JSON response if applicable
    return json_decode($response, true);
}



function getCurrencySymbol($currencyCode)
{
    if (extension_loaded('intl')) {
        try {
            // Create a NumberFormatter instance for currency
            $fmt = new NumberFormatter('en_PK', NumberFormatter::CURRENCY);

            // Set the currency code dynamically
            $fmt->setTextAttribute(NumberFormatter::CURRENCY_CODE, $currencyCode);

            // Get the currency symbol for the given currency code
            $symbol = $fmt->getSymbol(NumberFormatter::CURRENCY_SYMBOL);

            // Check if a symbol was returned, if not return the currency code
            if (empty($symbol)) {
                return $currencyCode; // Return the currency code if symbol is not found
            }

            return $symbol;
        } catch (Exception $e) {
            // In case of any error (invalid currency code), return a fallback symbol
            return $currencyCode;
        }
    }
    // Return the currency code if 'intl' extension is not loaded
    return $currencyCode;
}

// function getCurrencySymbol($currencyCode)
// {
//     // Mapping of currency codes to symbols
//     $currencySymbols = [
//         'USD' => 'US$',
//         'EUR' => '€',
//         'GBP' => '£',
//         'INR' => '₹',
//         'JPY' => '¥',
//         'CAD' => 'CA$',
//         'AUD' => 'A$',
//         'CNY' => '¥',
//         'PKR' => '₨',
//         'MXN' => 'MX$',
//         'BRL' => 'R$',
//         'ZAR' => 'R',
//         'CHF' => 'CHF',
//         'NZD' => 'NZ$',
//         'SEK' => 'kr',
//         'NOK' => 'kr',
//         'DKK' => 'kr',
//         'SAR' => 'ر.س',
//         'AED' => 'د.إ',
//         'TRY' => '₺',
//         'COP' => '$',
//     ];

//     // Return the currency symbol or the currency code if not found
//     return isset($currencySymbols[$currencyCode]) ? $currencySymbols[$currencyCode] : $currencyCode;
// }
function formatPrice($price,  $decimal_places = 0) {
    $decimalPlaces = isset($_COOKIE['decimal_places']) ? $_COOKIE['decimal_places'] : $decimal_places;
    if (!is_numeric($price)) {
        return $price;
    }
    return number_format($price, $decimalPlaces, '.', ',');
}

function debug($data){
    echo '<pre>';
    print_r($data);
    die();
}


function stripHtmlTags($input) {
    return strip_tags($input);
}

function createSlug($string){
    $string = preg_replace("/[^A-Za-z0-9]/", '-', $string);
    $string = preg_replace("/-+/", ' ', $string);
    $string = trim($string);
    return $string = str_replace(" ", "-", $string);
  }


